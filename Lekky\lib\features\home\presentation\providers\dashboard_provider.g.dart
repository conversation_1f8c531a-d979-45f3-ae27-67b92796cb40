// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dashboard_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$dashboardHash() => r'53eeb8e9ad475b7259a731ecc3ba4b762ac07c9d';

/// Dashboard provider with comprehensive home screen data management
///
/// Copied from [Dashboard].
@ProviderFor(Dashboard)
final dashboardProvider =
    AutoDisposeAsyncNotifierProvider<Dashboard, DashboardState>.internal(
  Dashboard.new,
  name: r'dashboardProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$dashboardHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Dashboard = AutoDisposeAsyncNotifier<DashboardState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
