// File: lib/features/history/presentation/services/history_data_loader.dart

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/providers/database_provider.dart';
import '../../../../core/shared/enums/entry_enums.dart';
import '../../../../core/utils/logger.dart';
import '../../domain/models/history_state.dart';
import '../../../meter_readings/domain/models/meter_reading.dart';
import '../../../top_ups/domain/models/top_up.dart';

/// Service for loading and processing history data
class HistoryDataLoader {
  /// Load entries with pagination and filtering
  static Future<Map<String, dynamic>> loadEntriesWithCount(
    Ref ref,
    HistoryState currentState,
  ) async {
    Logger.info(
        'HistoryDataLoader: Loading entries for ${currentState.filterType}');

    switch (currentState.filterType) {
      case EntryFilterType.all:
        return await _loadAllEntriesWithCount(ref, currentState);
      case EntryFilterType.meterReadings:
      case EntryFilterType.topUps:
      case EntryFilterType.invalid:
      case EntryFilterType.dismissed:
        return await _loadSingleTypeEntriesWithCount(ref, currentState);
    }
  }

  /// Load all entries (meter readings + top-ups) with pagination
  static Future<Map<String, dynamic>> _loadAllEntriesWithCount(
    Ref ref,
    HistoryState currentState,
  ) async {
    final meterReadingRepo = ref.read(meterReadingRepositoryProvider);
    final topUpRepo = ref.read(topUpRepositoryProvider);

    // Load ALL entries first
    final allMeterReadings = await meterReadingRepo.getAllMeterReadings();
    final allTopUps = await topUpRepo.getAllTopUps();

    // Combine and filter by date range if specified
    List<dynamic> allEntries = [...allMeterReadings, ...allTopUps];

    if (currentState.startDate != null && currentState.endDate != null) {
      allEntries = allEntries.where((entry) {
        final entryDate =
            entry is MeterReading ? entry.date : (entry as TopUp).date;
        return entryDate.isAfter(
                currentState.startDate!.subtract(const Duration(days: 1))) &&
            entryDate
                .isBefore(currentState.endDate!.add(const Duration(days: 1)));
      }).toList();
    }

    // Sort entries
    _sortEntries(allEntries, currentState.sortOrder);

    // Apply pagination
    final totalCount = allEntries.length;
    final startIndex = currentState.currentPage * currentState.entriesPerPage;
    final endIndex =
        (startIndex + currentState.entriesPerPage).clamp(0, totalCount);
    final paginatedEntries = allEntries.sublist(
      startIndex.clamp(0, totalCount).toInt(),
      endIndex.toInt(),
    );

    return {
      'entries': paginatedEntries,
      'totalCount': totalCount,
    };
  }

  /// Load single-type entries with pagination
  static Future<Map<String, dynamic>> _loadSingleTypeEntriesWithCount(
    Ref ref,
    HistoryState currentState,
  ) async {
    final meterReadingRepo = ref.read(meterReadingRepositoryProvider);
    final topUpRepo = ref.read(topUpRepositoryProvider);

    List<dynamic> allEntries = [];

    // Load specific type of entries
    switch (currentState.filterType) {
      case EntryFilterType.meterReadings:
        allEntries = await meterReadingRepo.getAllMeterReadings();
        break;
      case EntryFilterType.topUps:
        allEntries = await topUpRepo.getAllTopUps();
        break;
      case EntryFilterType.invalid:
        final meterReadings = await meterReadingRepo.getAllMeterReadings();
        final topUps = await topUpRepo.getAllTopUps();
        allEntries = [
          ...meterReadings.where((r) => r.status == EntryStatus.invalid),
          ...topUps.where((t) => t.status == EntryStatus.invalid),
        ];
        break;
      case EntryFilterType.dismissed:
        final meterReadings = await meterReadingRepo.getAllMeterReadings();
        allEntries = meterReadings
            .where((r) =>
                r.status == EntryStatus.ignored ||
                (r.notes?.contains('Records Gap:') == true))
            .toList();
        break;
      case EntryFilterType.all:
        // This case is handled by _loadAllEntriesWithCount
        break;
    }

    // Apply date range filter if specified
    if (currentState.startDate != null && currentState.endDate != null) {
      allEntries = allEntries.where((entry) {
        final entryDate =
            entry is MeterReading ? entry.date : (entry as TopUp).date;
        return entryDate.isAfter(
                currentState.startDate!.subtract(const Duration(days: 1))) &&
            entryDate
                .isBefore(currentState.endDate!.add(const Duration(days: 1)));
      }).toList();
    }

    // Sort entries
    _sortEntries(allEntries, currentState.sortOrder);

    // Apply pagination
    final totalCount = allEntries.length;
    final startIndex = currentState.currentPage * currentState.entriesPerPage;
    final endIndex =
        (startIndex + currentState.entriesPerPage).clamp(0, totalCount);
    final paginatedEntries = allEntries.sublist(
      startIndex.clamp(0, totalCount).toInt(),
      endIndex.toInt(),
    );

    return {
      'entries': paginatedEntries,
      'totalCount': totalCount,
    };
  }

  /// Sort entries by date
  static void _sortEntries(List<dynamic> entries, EntrySortOrder sortOrder) {
    entries.sort((a, b) {
      DateTime dateA = a is MeterReading ? a.date : (a as TopUp).date;
      DateTime dateB = b is MeterReading ? b.date : (b as TopUp).date;

      return sortOrder == EntrySortOrder.newestFirst
          ? dateB.compareTo(dateA)
          : dateA.compareTo(dateB);
    });
  }

  /// Get entry type for logging/debugging
  static String getEntryTypeString(dynamic entry) {
    return entry is MeterReading ? 'MeterReading' : 'TopUp';
  }

  /// Validate loaded data
  static bool validateLoadedData(Map<String, dynamic> result) {
    return result.containsKey('entries') &&
        result.containsKey('totalCount') &&
        result['entries'] is List &&
        result['totalCount'] is int;
  }
}
