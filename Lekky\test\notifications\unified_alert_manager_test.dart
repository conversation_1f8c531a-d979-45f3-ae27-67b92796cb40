import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../lib/core/services/unified_alert_manager.dart';
import '../../lib/core/constants/preference_keys.dart';
import '../../lib/features/notifications/domain/models/notification.dart';

void main() {
  group('UnifiedAlertManager Tests', () {
    late UnifiedAlertManager alertManager;

    setUp(() {
      alertManager = UnifiedAlertManager();
      // Mock SharedPreferences
      SharedPreferences.setMockInitialValues({});
    });

    test('should not check alerts when notifications disabled', () async {
      // Set up preferences with notifications disabled
      SharedPreferences.setMockInitialValues({
        PreferenceKeys.notificationsEnabled: false,
        PreferenceKeys.lowBalanceAlertsEnabled: true,
        PreferenceKeys.timeToTopUpAlertsEnabled: true,
      });

      // This should complete without throwing and not fire any notifications
      await alertManager.checkAndFireAlerts();
      
      // Test passes if no exceptions are thrown
      expect(true, isTrue);
    });

    test('should handle missing dashboard state gracefully', () async {
      // Set up preferences with notifications enabled but no data
      SharedPreferences.setMockInitialValues({
        PreferenceKeys.notificationsEnabled: true,
        PreferenceKeys.lowBalanceAlertsEnabled: true,
        PreferenceKeys.timeToTopUpAlertsEnabled: true,
        PreferenceKeys.alertThreshold: 10.0,
        PreferenceKeys.daysInAdvance: 3,
      });

      // This should complete without throwing even with no dashboard data
      await alertManager.checkAndFireAlerts();
      
      // Test passes if no exceptions are thrown
      expect(true, isTrue);
    });

    test('should respect deduplication settings', () async {
      final prefs = await SharedPreferences.getInstance();
      
      // Set notification as sent today
      await prefs.setString(
        PreferenceKeys.lastLowBalanceNotificationDate,
        DateTime.now().toIso8601String(),
      );

      // Set up preferences
      SharedPreferences.setMockInitialValues({
        PreferenceKeys.notificationsEnabled: true,
        PreferenceKeys.lowBalanceAlertsEnabled: true,
        PreferenceKeys.lastLowBalanceNotificationDate: DateTime.now().toIso8601String(),
      });

      // Should not fire duplicate notification
      await alertManager.checkAndFireAlerts();
      
      // Test passes if no exceptions are thrown
      expect(true, isTrue);
    });

    test('should create proper notification objects', () {
      // Test notification creation
      final notification = AppNotification(
        title: 'Test Alert',
        message: 'Test message',
        timestamp: DateTime.now(),
        type: NotificationType.lowBalance,
      );

      expect(notification.title, equals('Test Alert'));
      expect(notification.message, equals('Test message'));
      expect(notification.type, equals(NotificationType.lowBalance));
      expect(notification.isRead, isFalse);
    });

    test('should handle notification type priority correctly', () {
      final lowBalanceNotification = AppNotification(
        title: 'Low Balance',
        message: 'Low balance message',
        timestamp: DateTime.now(),
        type: NotificationType.lowBalance,
      );

      final reminderNotification = AppNotification(
        title: 'Reminder',
        message: 'Reminder message',
        timestamp: DateTime.now(),
        type: NotificationType.readingReminder,
      );

      // Low balance should have higher priority
      expect(lowBalanceNotification.priorityLevel, greaterThan(reminderNotification.priorityLevel));
    });
  });

  group('Notification Model Tests', () {
    test('should create notification with all required fields', () {
      final notification = AppNotification(
        title: 'Test Title',
        message: 'Test Message',
        timestamp: DateTime.now(),
        type: NotificationType.timeToTopUp,
      );

      expect(notification.title, equals('Test Title'));
      expect(notification.message, equals('Test Message'));
      expect(notification.type, equals(NotificationType.timeToTopUp));
      expect(notification.isRead, isFalse);
      expect(notification.id, isNull);
    });

    test('should create notification copy with modified fields', () {
      final original = AppNotification(
        title: 'Original Title',
        message: 'Original Message',
        timestamp: DateTime.now(),
        type: NotificationType.lowBalance,
      );

      final copy = original.copyWith(
        title: 'Modified Title',
        isRead: true,
      );

      expect(copy.title, equals('Modified Title'));
      expect(copy.message, equals('Original Message')); // Unchanged
      expect(copy.isRead, isTrue);
      expect(copy.type, equals(NotificationType.lowBalance)); // Unchanged
    });

    test('should convert notification to and from map', () {
      final notification = AppNotification(
        id: 123,
        title: 'Test Title',
        message: 'Test Message',
        timestamp: DateTime.parse('2024-01-01T12:00:00Z'),
        type: NotificationType.invalidRecord,
        isRead: true,
      );

      final map = notification.toMap();
      final reconstructed = AppNotification.fromMap(map);

      expect(reconstructed.id, equals(123));
      expect(reconstructed.title, equals('Test Title'));
      expect(reconstructed.message, equals('Test Message'));
      expect(reconstructed.timestamp, equals(DateTime.parse('2024-01-01T12:00:00Z')));
      expect(reconstructed.type, equals(NotificationType.invalidRecord));
      expect(reconstructed.isRead, isTrue);
    });
  });
}
