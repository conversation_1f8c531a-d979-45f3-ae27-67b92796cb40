import 'package:freezed_annotation/freezed_annotation.dart';

part 'welcome_state.freezed.dart';

/// State for the welcome screen
@freezed
class WelcomeState with _$WelcomeState {
  const factory WelcomeState({
    @Default(false) bool isLoading,
    String? error,
    @Default(false) bool isCompleted,
  }) = _WelcomeState;

  /// Default state
  const factory WelcomeState.initial() = _Initial;

  /// Loading state
  const factory WelcomeState.loading() = _Loading;

  /// Error state
  const factory WelcomeState.error(String message) = _Error;

  /// Completed state
  const factory WelcomeState.completed() = _Completed;
}

extension WelcomeStateX on WelcomeState {
  /// Whether the welcome screen has an error
  bool get hasError => maybeWhen(
        (isLoading, error, isCompleted) => error != null,
        error: (_) => true,
        orElse: () => false,
      );
}
