import 'package:flutter/material.dart';
import 'custom_date_picker_widget.dart';

/// Date constraints for date pickers
class DateConstraints {
  final DateTime firstDate;
  final DateTime lastDate;
  final DateTime defaultDate;

  const DateConstraints({
    required this.firstDate,
    required this.lastDate,
    required this.defaultDate,
  });
}

/// Widget that displays from and to date pickers side by side
class DateRangeSelectorWidget extends StatelessWidget {
  /// Currently selected from date
  final DateTime? fromDate;

  /// Currently selected to date
  final DateTime? toDate;

  /// Callback when from date changes
  final Function(DateTime) onFromDateChanged;

  /// Callback when to date changes
  final Function(DateTime) onToDateChanged;

  /// Callback when date range is cleared
  final VoidCallback? onClearDateRange;

  /// Function to get constraints for from date picker
  final Future<DateConstraints> Function() getFromDateConstraints;

  /// Function to get constraints for to date picker
  final Future<DateConstraints> Function() getToDateConstraints;

  /// Theme context for styling
  final String themeContext;

  /// Constructor
  const DateRangeSelectorWidget({
    super.key,
    required this.fromDate,
    required this.toDate,
    required this.onFromDateChanged,
    required this.onToDateChanged,
    required this.getFromDateConstraints,
    required this.getToDateConstraints,
    this.onClearDateRange,
    this.themeContext = 'history',
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenWidth = MediaQuery.of(context).size.width;
    final spacing = screenWidth < 360 ? 8.0 : 12.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Date Range',
              style: TextStyle(
                fontSize: 12,
                color: theme.colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
            const Spacer(),
            if (fromDate != null || toDate != null)
              IconButton(
                icon: const Icon(Icons.clear, size: 16),
                onPressed: onClearDateRange,
                tooltip: 'Clear date range',
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
              ),
          ],
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            // From date picker
            Expanded(
              child: FutureBuilder<DateConstraints>(
                future: getFromDateConstraints(),
                builder: (context, snapshot) {
                  if (!snapshot.hasData) {
                    return const SizedBox(
                      height: 36,
                      child: Center(child: CircularProgressIndicator()),
                    );
                  }

                  final constraints = snapshot.data!;
                  return CustomDatePickerWidget(
                    label: 'From',
                    selectedDate: fromDate,
                    onDateSelected: onFromDateChanged,
                    firstDate: constraints.firstDate,
                    lastDate: toDate ?? constraints.lastDate,
                    defaultDate: constraints.defaultDate,
                    themeContext: themeContext,
                  );
                },
              ),
            ),
            SizedBox(width: spacing),
            // To date picker
            Expanded(
              child: FutureBuilder<DateConstraints>(
                future: getToDateConstraints(),
                builder: (context, snapshot) {
                  if (!snapshot.hasData) {
                    return const SizedBox(
                      height: 36,
                      child: Center(child: CircularProgressIndicator()),
                    );
                  }

                  final constraints = snapshot.data!;
                  return CustomDatePickerWidget(
                    label: 'To',
                    selectedDate: toDate,
                    onDateSelected: onToDateChanged,
                    firstDate: fromDate ?? constraints.firstDate,
                    lastDate: constraints.lastDate,
                    defaultDate: constraints.defaultDate,
                    themeContext: themeContext,
                  );
                },
              ),
            ),
          ],
        ),
      ],
    );
  }
}
