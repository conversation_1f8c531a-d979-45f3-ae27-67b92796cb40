// File: lib/features/setup/presentation/screens/setup_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../widgets/date_settings_card.dart';
import '../widgets/alert_settings_card.dart';
import '../widgets/meter_reading_card.dart';
import '../widgets/region_settings_card.dart';
import '../widgets/appearance_settings_card.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/providers/preference_provider.dart';
import '../../../../core/providers/settings_provider.dart';
import '../../../../core/models/settings_state.dart';

import '../../domain/models/setup_preferences.dart' as domain;

import 'package:lekky/core/shared/models/date_format.dart';
import 'package:lekky/core/shared/models/theme_mode.dart';

class SetupPreferences {
  String language;
  String currency;
  String currencySymbol;
  double? initialMeterReading;
  double alertThreshold;
  int daysInAdvance;
  DateFormat dateFormat;
  DateInfoType showTimeWithDate;
  AppThemeMode themeMode;

  SetupPreferences({
    required this.language,
    required this.currency,
    required this.currencySymbol,
    this.initialMeterReading,
    required this.alertThreshold,
    required this.daysInAdvance,
    required this.dateFormat,
    required this.showTimeWithDate,
    required this.themeMode,
  });
}

final setupProvider =
    StateNotifierProvider<SetupStateNotifier, SetupState>((ref) {
  return SetupStateNotifier(ref);
});

class SetupState {
  final SetupPreferences preferences;
  final bool isLoading;
  final String? error;

  SetupState({
    required this.preferences,
    this.isLoading = false,
    this.error,
  });

  SetupState copyWith({
    SetupPreferences? preferences,
    bool? isLoading,
    String? error,
  }) {
    return SetupState(
      preferences: preferences ?? this.preferences,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }
}

class SetupStateNotifier extends StateNotifier<SetupState> {
  final Ref ref;

  SetupStateNotifier(this.ref)
      : super(SetupState(
          preferences: SetupPreferences(
            language: 'English',
            currency: 'USD',
            currencySymbol: '\$',
            alertThreshold: 10.0,
            daysInAdvance: 5,
            dateFormat: DateFormat.ddMmYyyy,
            showTimeWithDate: DateInfoType.dateOnly,
            themeMode: AppThemeMode.system,
          ),
          isLoading: true,
        ));

  Future<void> initialize() async {
    // Use ref.read to fetch the resolved SettingsState
    final settings = await ref.read(settingsProvider.future);
    state = SetupState(
      preferences: SetupPreferences(
        language: settings.language,
        currency: settings.currency,
        currencySymbol: settings.currencySymbol,
        initialMeterReading: null,
        alertThreshold: settings.alertThreshold,
        daysInAdvance: settings.daysInAdvance,
        dateFormat: DateFormat.fromString(settings.dateFormat),
        showTimeWithDate: settings.showTimeWithDate
            ? DateInfoType.dateAndTime
            : DateInfoType.dateOnly,
        themeMode: settings.themeMode,
      ),
      isLoading: false,
    );
  }

  void setLanguage(String language) {
    ref.read(settingsProvider.notifier).updateLanguage(language);
    state = state.copyWith(
      preferences: SetupPreferences(
        language: language,
        currency: state.preferences.currency,
        currencySymbol: state.preferences.currencySymbol,
        initialMeterReading: state.preferences.initialMeterReading,
        alertThreshold: state.preferences.alertThreshold,
        daysInAdvance: state.preferences.daysInAdvance,
        dateFormat: state.preferences.dateFormat,
        showTimeWithDate: state.preferences.showTimeWithDate,
        themeMode: state.preferences.themeMode,
      ),
    );
  }

  void setCurrency(String currency, String currencySymbol) {
    ref
        .read(settingsProvider.notifier)
        .updateCurrency(currency, currencySymbol);
    state = state.copyWith(
      preferences: SetupPreferences(
        language: state.preferences.language,
        currency: currency,
        currencySymbol: currencySymbol,
        initialMeterReading: state.preferences.initialMeterReading,
        alertThreshold: state.preferences.alertThreshold,
        daysInAdvance: state.preferences.daysInAdvance,
        dateFormat: state.preferences.dateFormat,
        showTimeWithDate: state.preferences.showTimeWithDate,
        themeMode: state.preferences.themeMode,
      ),
    );
  }

  void setInitialMeterReading(double? value) {
    state = state.copyWith(
      preferences: SetupPreferences(
        language: state.preferences.language,
        currency: state.preferences.currency,
        currencySymbol: state.preferences.currencySymbol,
        initialMeterReading: value,
        alertThreshold: state.preferences.alertThreshold,
        daysInAdvance: state.preferences.daysInAdvance,
        dateFormat: state.preferences.dateFormat,
        showTimeWithDate: state.preferences.showTimeWithDate,
        themeMode: state.preferences.themeMode,
      ),
    );
  }

  void setAlertThreshold(double value) {
    ref.read(settingsProvider.notifier).updateAlertThreshold(value);
    state = state.copyWith(
      preferences: SetupPreferences(
        language: state.preferences.language,
        currency: state.preferences.currency,
        currencySymbol: state.preferences.currencySymbol,
        initialMeterReading: state.preferences.initialMeterReading,
        alertThreshold: value,
        daysInAdvance: state.preferences.daysInAdvance,
        dateFormat: state.preferences.dateFormat,
        showTimeWithDate: state.preferences.showTimeWithDate,
        themeMode: state.preferences.themeMode,
      ),
    );
  }

  void setDaysInAdvance(int value) {
    ref.read(settingsProvider.notifier).updateDaysInAdvance(value);
    state = state.copyWith(
      preferences: SetupPreferences(
        language: state.preferences.language,
        currency: state.preferences.currency,
        currencySymbol: state.preferences.currencySymbol,
        initialMeterReading: state.preferences.initialMeterReading,
        alertThreshold: state.preferences.alertThreshold,
        daysInAdvance: value,
        dateFormat: state.preferences.dateFormat,
        showTimeWithDate: state.preferences.showTimeWithDate,
        themeMode: state.preferences.themeMode,
      ),
    );
  }

  void setDateFormat(DateFormat format) {
    ref.read(settingsProvider.notifier).updateDateFormat(format);
    state = state.copyWith(
      preferences: SetupPreferences(
        language: state.preferences.language,
        currency: state.preferences.currency,
        currencySymbol: state.preferences.currencySymbol,
        initialMeterReading: state.preferences.initialMeterReading,
        alertThreshold: state.preferences.alertThreshold,
        daysInAdvance: state.preferences.daysInAdvance,
        dateFormat: format,
        showTimeWithDate: state.preferences.showTimeWithDate,
        themeMode: state.preferences.themeMode,
      ),
    );
  }

  void setShowTimeWithDate(DateInfoType value) {
    ref
        .read(settingsProvider.notifier)
        .updateShowTimeWithDate(value == DateInfoType.dateAndTime);
    state = state.copyWith(
      preferences: SetupPreferences(
        language: state.preferences.language,
        currency: state.preferences.currency,
        currencySymbol: state.preferences.currencySymbol,
        initialMeterReading: state.preferences.initialMeterReading,
        alertThreshold: state.preferences.alertThreshold,
        daysInAdvance: state.preferences.daysInAdvance,
        dateFormat: state.preferences.dateFormat,
        showTimeWithDate: value,
        themeMode: state.preferences.themeMode,
      ),
    );
  }

  void setThemeMode(AppThemeMode mode) {
    ref.read(settingsProvider.notifier).updateThemeMode(mode);
    state = state.copyWith(
      preferences: SetupPreferences(
        language: state.preferences.language,
        currency: state.preferences.currency,
        currencySymbol: state.preferences.currencySymbol,
        initialMeterReading: state.preferences.initialMeterReading,
        alertThreshold: state.preferences.alertThreshold,
        daysInAdvance: state.preferences.daysInAdvance,
        dateFormat: state.preferences.dateFormat,
        showTimeWithDate: state.preferences.showTimeWithDate,
        themeMode: mode,
      ),
    );
  }

  bool validatePreferences() {
    final prefs = state.preferences;
    if (prefs.initialMeterReading != null &&
        (prefs.initialMeterReading! < 0.0 ||
            prefs.initialMeterReading! > 999.99)) {
      state = state.copyWith(
          error: 'Initial meter reading must be between 0.00 and 999.99');
      return false;
    }
    final settingsState = SettingsState(
      language: prefs.language,
      currency: prefs.currency,
      currencySymbol: prefs.currencySymbol,
      alertThreshold: prefs.alertThreshold,
      daysInAdvance: prefs.daysInAdvance,
      dateFormat: prefs.dateFormat.formatString,
      showTimeWithDate: prefs.showTimeWithDate == DateInfoType.dateAndTime,
      themeMode: prefs.themeMode,
    );
    if (!settingsState.isValid) {
      state = state.copyWith(
          error:
              'Invalid settings: ${settingsState.validationErrors.join(', ')}');
      return false;
    }
    return true;
  }

  Future<bool> savePreferences() async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      final prefs = await SharedPreferences.getInstance();
      if (state.preferences.initialMeterReading != null) {
        await prefs.setDouble(
            'initialMeterReading', state.preferences.initialMeterReading!);
      }
      // Note: Setup completion is handled by SettingsProvider.completeSetup()
      state = state.copyWith(isLoading: false);
      return true;
    } catch (e) {
      state = state.copyWith(
          isLoading: false, error: 'Failed to save preferences: $e');
      return false;
    }
  }
}

class SetupScreen extends ConsumerStatefulWidget {
  const SetupScreen({super.key});

  @override
  ConsumerState<SetupScreen> createState() => _SetupScreenState();
}

class _SetupScreenState extends ConsumerState<SetupScreen> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    ref.read(setupProvider.notifier).initialize();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final setupState = ref.watch(setupProvider);

    if (setupState.isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (setupState.error != null) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 48,
              ),
              const SizedBox(height: 16),
              Text(
                'Error: ${setupState.error}',
                style: const TextStyle(color: Colors.red),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  ref.invalidate(setupProvider);
                  ref.read(setupProvider.notifier).initialize();
                },
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Setup'),
        backgroundColor: const Color(0xFF424242),
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              controller: _scrollController,
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Region Settings',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Configure language and currency preferences.',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 16),
                  const RegionSettingsCard(),
                  const SizedBox(height: 32),
                  const Text(
                    'Initial Meter Reading',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Enter your current meter reading to start tracking.',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 16),
                  MeterReadingCard(
                    initialMeterReading:
                        setupState.preferences.initialMeterReading,
                    onInitialMeterReadingChanged:
                        ref.read(setupProvider.notifier).setInitialMeterReading,
                    currencySymbol: setupState.preferences.currencySymbol,
                  ),
                  const SizedBox(height: 32),
                  const Text(
                    'Alert Settings',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Configure when you want to receive alerts about your meter balance.',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 16),
                  AlertSettingsCard(
                    alertThreshold: setupState.preferences.alertThreshold,
                    daysInAdvance: setupState.preferences.daysInAdvance,
                    currencySymbol: setupState.preferences.currencySymbol,
                    onAlertThresholdChanged:
                        ref.read(setupProvider.notifier).setAlertThreshold,
                    onDaysInAdvanceChanged:
                        ref.read(setupProvider.notifier).setDaysInAdvance,
                  ),
                  const SizedBox(height: 32),
                  const Text(
                    'Date Settings',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Configure how dates are displayed in the app.',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 16),
                  DateSettingsCard(
                    dateFormat: setupState.preferences.dateFormat,
                    showTimeWithDate: setupState.preferences.showTimeWithDate ==
                        DateInfoType.dateAndTime,
                    onDateFormatChanged:
                        ref.read(setupProvider.notifier).setDateFormat,
                    onShowTimeWithDateChanged: (bool value) {
                      ref.read(setupProvider.notifier).setShowTimeWithDate(
                            value
                                ? DateInfoType.dateAndTime
                                : DateInfoType.dateOnly,
                          );
                    },
                  ),
                  const SizedBox(height: 32),
                  const Text(
                    'Appearance',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Customize the look and feel of the app.',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 16),
                  AppearanceSettingsCard(
                    themeMode: setupState.preferences.themeMode,
                    onThemeModeChanged:
                        ref.read(setupProvider.notifier).setThemeMode,
                  ),
                  const SizedBox(height: 32),
                ],
              ),
            ),
          ),
          _buildNavigationButtons(),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Icon(
            icon,
            color: Theme.of(context).colorScheme.primary,
            size: 24,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          ElevatedButton(
            onPressed: () async {
              final setupNotifier = ref.read(setupProvider.notifier);
              if (setupNotifier.validatePreferences()) {
                try {
                  // Convert Riverpod setup preferences to domain model
                  final setupState = ref.read(setupProvider);
                  final setupPrefs = setupState.preferences;
                  final domainPrefs = domain.SetupPreferences(
                    language: setupPrefs.language,
                    currency: setupPrefs.currency,
                    currencySymbol: setupPrefs.currencySymbol,
                    initialMeterReading: setupPrefs.initialMeterReading,
                    alertThreshold: setupPrefs.alertThreshold,
                    daysInAdvance: setupPrefs.daysInAdvance,
                    dateFormat: setupPrefs.dateFormat,
                    showTimeWithDate:
                        setupPrefs.showTimeWithDate == DateInfoType.dateAndTime,
                    themeMode: setupPrefs.themeMode,
                  );

                  // Use Riverpod settings provider for setup completion
                  final result = await ref
                      .read(settingsProvider.notifier)
                      .completeSetup(domainPrefs);

                  if (result.success) {
                    // Update Riverpod state
                    await ref
                        .read(preferencesProvider.notifier)
                        .markSetupComplete();
                    ref.invalidate(settingsProvider);

                    if (mounted) {
                      if (result.hasError) {
                        // Show warning but still navigate
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                                'Setup completed with warning: ${result.error}'),
                            backgroundColor: Colors.orange,
                          ),
                        );
                      }
                      context.go(AppConstants.routeHome);
                    }
                  } else {
                    if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                              'Setup failed: ${result.error ?? "Unknown error"}'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                } catch (e) {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Setup failed: ${e.toString()}'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Please check your inputs.'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            child: const Text('Finish Setup'),
          ),
        ],
      ),
    );
  }
}
