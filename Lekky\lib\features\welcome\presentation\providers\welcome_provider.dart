import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../domain/models/welcome_state.dart';
import '../../../../core/utils/logger.dart';

part 'welcome_provider.g.dart';

/// Provider for welcome screen management using Riverpod
@riverpod
class Welcome extends _$Welcome {
  @override
  Future<WelcomeState> build() async {
    return const WelcomeState.initial();
  }

  /// Mark the welcome screen as completed
  /// Note: Welcome completion is no longer tracked separately
  /// Setup completion is the authoritative gate for onboarding
  Future<void> markWelcomeCompleted() async {
    try {
      state = const AsyncValue.loading();
      
      // Welcome completion is no longer tracked separately
      // Setup completion is the authoritative gate for onboarding
      await Future.delayed(const Duration(milliseconds: 100)); // Minimal delay for UI feedback
      
      state = const AsyncValue.data(WelcomeState.completed());
      Logger.info('Welcome screen marked as completed');
    } catch (e, stackTrace) {
      Logger.error('Failed to mark welcome as completed: $e');
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Clear any error state
  void clearError() {
    if (state.hasError) {
      state = const AsyncValue.data(WelcomeState.initial());
    }
  }
}
