import msal
import requests

# Azure AD app credentials
CLIENT_ID = "your-client-id"
CLIENT_SECRET = "your-client-secret"
TENANT_ID = "your-tenant-id"

AUTHORITY = f"https://login.microsoftonline.com/{TENANT_ID}"
SCOPE = ["https://graph.microsoft.com/.default"]

# Authenticate and get token
def get_access_token():
    app = msal.ConfidentialClientApplication(
        CLIENT_ID, authority=AUTHORITY, client_credential=CLIENT_SECRET
    )
    result = app.acquire_token_for_client(scopes=SCOPE)
    if "access_token" in result:
        return result["access_token"]
    else:
        raise Exception("Failed to get access token")

# Search emails matching from/to and keywords
def search_emails(token, person_email, keywords):
    headers = {"Authorization": f"Bearer {token}"}
    query = f"""
        from:{person_email} OR to:{person_email} AND
        ({' OR '.join(f'body:{kw}' for kw in keywords)})
    """
    # Microsoft Graph API message search (filter syntax)
    # Using advanced query is limited, so use $search param
    url = "https://graph.microsoft.com/v1.0/me/messages"
    params = {
        "$search": query,
        "$top": 50,
        "$select": "subject,bodyPreview,receivedDateTime,from,toRecipients"
    }
    response = requests.get(url, headers=headers, params=params)
    response.raise_for_status()
    data = response.json()
    return data.get("value", [])

# Fetch full body of emails if needed
def get_email_body(token, message_id):
    headers = {"Authorization": f"Bearer {token}"}
    url = f"https://graph.microsoft.com/v1.0/me/messages/{message_id}"
    params = {"$select": "body"}
    response = requests.get(url, headers=headers, params=params)
    response.raise_for_status()
    return response.json().get("body", {}).get("content", "")

