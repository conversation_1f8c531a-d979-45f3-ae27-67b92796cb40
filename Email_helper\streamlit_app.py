import streamlit as st
import requests
import msal
from datetime import datetime
import re
import os
from dotenv import load_dotenv
import pandas as pd
import time
import json

# Load environment variables
load_dotenv()

# Configuration from .env file
CLIENT_ID = os.getenv("CLIENT_ID")
TENANT_ID = os.getenv("TENANT_ID")
REDIRECT_URI = os.getenv("REDIRECT_URI")

# Graph API scope
SCOPE = ["Mail.Read", "User.Read"]

# Microsoft Graph API URL for emails
GRAPH_API_URL = "https://graph.microsoft.com/v1.0/me/messages?$top=1000"

st.set_page_config(page_title="📧 Email Report Generator - House Repairs & More")

@st.cache_resource
def get_msal_app():
    return msal.PublicClientApplication(
        client_id=CLIENT_ID,
        authority=f"https://login.microsoftonline.com/{TENANT_ID}"
    )

def wildcard_to_regex(pattern):
    pattern = re.escape(pattern).replace(r'\*', '.*')
    return f'^{pattern}$'

def authenticate():
    app = get_msal_app()
    result = app.initiate_device_flow(scopes=SCOPE)

    # Display instructions
    st.markdown("### 🔐 Please complete authentication")
    st.markdown(f"**Go to:** [https://microsoft.com/devicelogin](https://microsoft.com/devicelogin) and enter the code below:")
    st.info(result["user_code"])

    while True:
        result = app.acquire_token_with_device_code(device_code=result["device_code"], scopes=SCOPE)
        if "access_token" in result:
            st.success("✅ Authentication successful!")
            return result["access_token"]
        elif "error" in result and result.get("error") == "authorization_pending":
            time.sleep(1)  # Wait for user to authenticate
        else:
            error = result.get("error", "Unknown Error")
            message = result.get("error_description", "")
            st.error(f"Authentication failed: {error} - {message}")
            return None

def fetch_emails(token):
    headers = {"Authorization": f"Bearer {token}"}
    messages = []
    endpoint = GRAPH_API_URL

    while endpoint:
        try:
            response = requests.get(endpoint, headers=headers)
            time.sleep(1)  # Avoid rate limits
            if response.status_code != 200:
                st.error(f"Failed to fetch emails. Status: {response.status_code}")
                return []
            data = response.json()
            messages.extend(data.get("value", []))
            endpoint = data.get("@odata.nextLink")
        except Exception as e:
            st.error(f"Error fetching emails: {str(e)}")
            return []
    return messages

def matches_filters(email, sender, recipient, subject, body, start_date, end_date):
    from_email = email.get("from", {}).get("emailAddress", {}).get("address", "")
    to_emails = ", ".join([r["emailAddress"]["address"] for r in email.get("toRecipients", [])])
    subject_text = email.get("subject", "")
    body_text = email.get("body", {}).get("content", "")
    sent_date = email.get("sentDateTime", "")

    # Parse date
    try:
        email_date = datetime.strptime(sent_date, "%Y-%m-%dT%H:%M:%SZ")
    except ValueError:
        email_date = datetime.strptime(sent_date, "%Y-%m-%dT%H:%M:%S.%fZ")

    def match(text, pattern):
        return bool(re.search(wildcard_to_regex(pattern), text, re.IGNORECASE))

    return (
        (not sender or match(from_email, sender)) and
        (not recipient or match(to_emails, recipient)) and
        (not subject or match(subject_text, subject)) and
        (not body or match(body_text, body)) and
        (start_date <= email_date.date() <= end_date)
    )

def export_to_csv(emails):
    data = [{
        "Subject": e.get("subject", ""),
        "Date": e.get("sentDateTime", ""),
        "From": e.get("from", {}).get("emailAddress", {}).get("address", ""),
        "To": ", ".join([r["emailAddress"]["address"] for r in e.get("toRecipients", [])]),
        "Body": e.get("body", {}).get("content", "")[:1000]
    } for e in emails]
    df = pd.DataFrame(data)
    csv = df.to_csv(index=False, encoding="utf-8-sig")
    st.download_button(label="📥 Download CSV", data=csv, file_name="report.csv", mime="text/csv")

def display_report(emails):
    if not emails:
        st.warning("No matching emails found.")
        return

    for email in emails[:20]:  # Display up to 20 emails
        with st.expander(f"📧 {email.get('subject', 'No Subject')}", expanded=False):
            col1, col2 = st.columns([1, 3])
            with col1:
                st.markdown("📅 **Date:** " + email.get("sentDateTime", ""))
                st.markdown("📤 **From:** " + email.get("from", {}).get("emailAddress", {}).get("address", ""))
                st.markdown("📥 **To:** " + ", ".join([r["emailAddress"]["address"] for r in email.get("toRecipients", [])]))
            with col2:
                st.markdown(email.get("body", {}).get("content", "")[:1000])

def main():
    st.title("📧 Email Report Generator - House Repairs & More")

    # Authentication
    if "token" not in st.session_state:
        token = authenticate()
        if token:
            st.session_state.token = token

    # Skip to report display if authenticated
    if "token" not in st.session_state or not st.session_state.token:
        return

    # Filter form
    with st.form(key="email_filter_form"):
        sender = st.text_input("From (supports * wildcard)", value="*alaina*andrew*")
        recipient = st.text_input("To (supports * wildcard)", value="*alaina*andrew*")
        subject = st.text_input("Subject (supports * wildcard)", value="*house*repair*")
        body = st.text_input("Body contains (supports * wildcard)", value="")

        col1, col2 = st.columns(2)
        with col1:
            start_date = st.date_input("Start Date", value=datetime(2024, 1, 1))
        with col2:
            end_date = st.date_input("End Date", value=datetime.now().date())

        submitted = st.form_submit_button("🔍 Generate Report")

    if submitted:
        with st.spinner("Fetching emails from Microsoft Graph..."):
            emails = fetch_emails(st.session_state.token)
            filtered = [e for e in emails if matches_filters(e, sender, recipient, subject, body, start_date, end_date)]

        # Display results
        st.success(f"✅ Found {len(filtered)} matching emails")
        display_report(filtered)

        # Export to CSV
        if len(filtered) > 0:
            export_to_csv(filtered)

if __name__ == "__main__":
    main()
