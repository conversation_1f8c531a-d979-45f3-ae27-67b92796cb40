import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/di/service_locator.dart';
import '../../../notifications/data/notification_service.dart';
import '../../../notifications/domain/models/notification.dart';
import '../../presentation/providers/reminder_provider.dart';
import 'background_reminder_flags.dart';

/// Service for handling foreground resume and processing background flags
class ForegroundResumeHandler {
  static final ForegroundResumeHandler _instance =
      ForegroundResumeHandler._internal();
  factory ForegroundResumeHandler() => _instance;
  ForegroundResumeHandler._internal();

  /// Process pending background flags when app resumes
  static Future<void> processBackgroundFlags(WidgetRef ref) async {
    try {
      Logger.info('ForegroundResumeHandler: Processing background flags');
      Logger.info('ForegroundResumeHandler: processBackgroundFlags called');

      // Cleanup stale flags first
      await BackgroundReminderFlags.cleanupStaleFlags();

      // Check if any flags are set
      if (!await BackgroundReminderFlags.hasAnyFlags()) {
        Logger.info('ForegroundResumeHandler: No pending flags to process');
        return;
      }

      final flags = await BackgroundReminderFlags.getAllPendingFlags();
      Logger.info(
          'ForegroundResumeHandler: Found ${flags.length} pending flags');

      // Process flags in priority order
      await _processPendingNotification(ref, flags);
      await _processReminderFired(ref, flags);
      await _processSchedulingError(ref, flags);

      // Clear processed flags
      await BackgroundReminderFlags.clearAllFlags();

      Logger.info(
          'ForegroundResumeHandler: Completed processing background flags');
    } catch (e) {
      Logger.error(
          'ForegroundResumeHandler: Error processing background flags: $e');
    }
  }

  /// Process pending notification flag
  static Future<void> _processPendingNotification(
    WidgetRef ref,
    Map<String, dynamic> flags,
  ) async {
    final pendingNotificationId = flags['pendingNotificationId'] as int?;
    if (pendingNotificationId == null) return;

    try {
      Logger.info(
          'ForegroundResumeHandler: Processing pending notification ID: $pendingNotificationId');

      // Create and show notification in app
      final notification = AppNotification(
        id: pendingNotificationId,
        title: 'Meter Reading Reminder',
        message: 'It\'s time to take a new meter reading.',
        timestamp: DateTime.now(),
        type: NotificationType.readingReminder,
      );

      final notificationService =
          await serviceLocator.getAsync<NotificationService>();
      await notificationService.showNotification(notification);

      // Clear the flag
      await BackgroundReminderFlags.clearPendingNotificationId();

      Logger.info(
          'ForegroundResumeHandler: Successfully processed pending notification');
    } catch (e) {
      Logger.error(
          'ForegroundResumeHandler: Error processing pending notification: $e');
    }
  }

  /// Process reminder fired flag
  static Future<void> _processReminderFired(
    WidgetRef ref,
    Map<String, dynamic> flags,
  ) async {
    final reminderFired = flags['reminderFired'] as bool?;
    if (reminderFired != true) return;

    try {
      Logger.info('ForegroundResumeHandler: Processing reminder fired flag');
      Logger.info('ForegroundResumeHandler: _processReminderFired called');

      // Trigger reminder provider to handle firing and auto-reschedule
      final reminderNotifier = ref.read(reminderProvider.notifier);
      await reminderNotifier.onReminderFired();

      // Clear the flag
      await BackgroundReminderFlags.clearReminderFired();
      Logger.info(
          'ForegroundResumeHandler: _processReminderFired - Cleared reminder fired flag');

      Logger.info(
          'ForegroundResumeHandler: Successfully processed reminder fired');
    } catch (e) {
      Logger.error(
          'ForegroundResumeHandler: Error processing reminder fired: $e');
    }
  }

  /// Process scheduling error flag
  static Future<void> _processSchedulingError(
    WidgetRef ref,
    Map<String, dynamic> flags,
  ) async {
    final schedulingError = flags['schedulingError'] as String?;
    if (schedulingError == null) return;

    try {
      Logger.info(
          'ForegroundResumeHandler: Processing scheduling error: $schedulingError');

      // Show user notification about the error
      await _showErrorNotification(schedulingError);

      // Clear the flag
      await BackgroundReminderFlags.clearSchedulingError();

      Logger.info(
          'ForegroundResumeHandler: Successfully processed scheduling error');
    } catch (e) {
      Logger.error(
          'ForegroundResumeHandler: Error processing scheduling error: $e');
    }
  }

  /// Show error notification to user
  static Future<void> _showErrorNotification(String errorMessage) async {
    try {
      final notification = AppNotification(
        title: 'Reminder Error',
        message: 'Failed to schedule reminder: $errorMessage',
        timestamp: DateTime.now(),
        type: NotificationType.invalidRecord, // Reuse existing error type
      );

      final notificationService =
          await serviceLocator.getAsync<NotificationService>();
      await notificationService.showNotification(notification);
    } catch (e) {
      Logger.error(
          'ForegroundResumeHandler: Error showing error notification: $e');
    }
  }

  /// Handle app lifecycle state changes
  static Future<void> handleAppLifecycleStateChange(
    WidgetRef ref,
    String state,
  ) async {
    switch (state) {
      case 'resumed':
        Logger.info(
            'ForegroundResumeHandler: App resumed, checking for background flags');
        await processBackgroundFlags(ref);
        break;
      case 'paused':
        Logger.info('ForegroundResumeHandler: App paused');
        break;
      case 'inactive':
        Logger.info('ForegroundResumeHandler: App inactive');
        break;
      case 'detached':
        Logger.info('ForegroundResumeHandler: App detached');
        break;
      default:
        Logger.info('ForegroundResumeHandler: Unknown app state: $state');
    }
  }

  /// Initialize foreground resume handling
  static Future<void> initialize(WidgetRef ref) async {
    try {
      Logger.info('ForegroundResumeHandler: Initializing');

      // Process any existing flags on startup
      await processBackgroundFlags(ref);

      Logger.info('ForegroundResumeHandler: Initialization complete');
    } catch (e) {
      Logger.error('ForegroundResumeHandler: Error during initialization: $e');
    }
  }

  /// Check for missed reminders and handle them
  static Future<void> checkForMissedReminders(WidgetRef ref) async {
    try {
      Logger.info('ForegroundResumeHandler: Checking for missed reminders');

      // Get current reminder state
      final reminderState = ref.read(reminderProvider);

      await reminderState.when(
        data: (state) async {
          if (state.nextReminderDate != null &&
              state.nextReminderDate!.isBefore(DateTime.now())) {
            Logger.info(
                'ForegroundResumeHandler: Found missed reminder, triggering auto-reschedule');

            // Trigger reminder fired to handle missed reminder
            final reminderNotifier = ref.read(reminderProvider.notifier);
            await reminderNotifier.onReminderFired();
          }
        },
        loading: () async {
          Logger.info(
              'ForegroundResumeHandler: Reminder state loading, skipping missed reminder check');
        },
        error: (error, stack) async {
          Logger.error(
              'ForegroundResumeHandler: Error in reminder state, skipping missed reminder check: $error');
        },
      );
    } catch (e) {
      Logger.error(
          'ForegroundResumeHandler: Error checking for missed reminders: $e');
    }
  }

  /// Handle notification tap from background
  static Future<void> handleNotificationTap(
    WidgetRef ref,
    int notificationId,
    String notificationType,
  ) async {
    try {
      Logger.info(
          'ForegroundResumeHandler: Handling notification tap: $notificationId, type: $notificationType');

      if (notificationType == 'readingReminder') {
        // Set reminder fired flag for processing
        Logger.info(
            'ForegroundResumeHandler: Calling BackgroundReminderFlags.setReminderFired from handleNotificationTap');
        await BackgroundReminderFlags.setReminderFired(
          reminderDate: DateTime.now(),
          metadata: {
            'triggered_by': 'notification_tap',
            'notification_id': notificationId,
          },
        );
        Logger.info(
            'ForegroundResumeHandler: Called BackgroundReminderFlags.setReminderFired from handleNotificationTap');

        // Process the flag immediately
        await _processReminderFired(ref, {'reminderFired': true});
      }
    } catch (e) {
      Logger.error(
          'ForegroundResumeHandler: Error handling notification tap: $e');
    }
  }
}
