import 'package:flutter/material.dart';

/// Animated battery icon widget that provides dynamic animations based on days remaining
///
/// Battery icons and colors change based on timeframes:
/// - > 30 days: battery_full (green) - static
/// - 14-30 days: battery_6_bar (light green) - static
/// - 7-14 days: battery_5_bar (yellow) - gentle pulsing
/// - 1-7 days: battery_3_bar (orange) - blinking
/// - ≤ 1 day: battery_0_bar (red) - fast blinking
class AnimatedBatteryIcon extends StatefulWidget {
  final double? daysRemaining;
  final Color color;
  final double size;

  const AnimatedBatteryIcon({
    super.key,
    required this.daysRemaining,
    required this.color,
    this.size = 16,
  });

  @override
  State<AnimatedBatteryIcon> createState() => _AnimatedBatteryIconState();
}

class _AnimatedBatteryIconState extends State<AnimatedBatteryIcon>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _blinkController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _blinkAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startAppropriateAnimation();
  }

  @override
  void didUpdateWidget(AnimatedBatteryIcon oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.daysRemaining != widget.daysRemaining) {
      _startAppropriateAnimation();
    }
  }

  void _setupAnimations() {
    // Pulse animation for medium battery (7-14 days)
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    // Blink animation for critical/low battery (1-7 days)
    _blinkController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _blinkAnimation = Tween<double>(
      begin: 0.3,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _blinkController,
      curve: Curves.easeInOut,
    ));
  }

  void _startAppropriateAnimation() {
    // Stop all animations first
    _pulseController.stop();
    _blinkController.stop();

    if (widget.daysRemaining == null) return;

    final days = widget.daysRemaining!;

    if (days <= 1) {
      // Critical (24 hours): Fast blinking
      _blinkController.repeat(reverse: true);
    } else if (days <= 7) {
      // Low (7 days): Slower blinking
      _blinkController.repeat(reverse: true);
    } else if (days <= 14) {
      // Medium (14 days): Gentle pulsing
      _pulseController.repeat(reverse: true);
    }
    // For days > 14, no animation (static icon)
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _blinkController.dispose();
    super.dispose();
  }

  /// Get battery icon based on specific timeframes
  IconData _getBatteryIconForTimeframe(double days) {
    if (days <= 1) {
      return Icons.battery_0_bar; // Battery empty (24 hours or less)
    } else if (days <= 7) {
      return Icons.battery_3_bar; // Battery quarter full (7 days or less)
    } else if (days <= 14) {
      return Icons.battery_5_bar; // Battery half full (14 days or less)
    } else if (days <= 30) {
      return Icons.battery_6_bar; // Battery almost full (30 days or less)
    } else {
      return Icons.battery_full; // Battery full (more than 30 days)
    }
  }

  /// Get battery color based on specific timeframes
  Color _getBatteryColorForTimeframe(double days) {
    if (days <= 1) {
      return Colors.red; // Critical - 24 hours or less
    } else if (days <= 7) {
      return Colors.orange; // Low - 7 days or less
    } else if (days <= 14) {
      return Colors.yellow; // Medium - 14 days or less
    } else if (days <= 30) {
      return Colors.lightGreen; // Good - 30 days or less
    } else {
      return Colors.green; // Excellent - more than 30 days
    }
  }

  @override
  Widget build(BuildContext context) {
    final days = widget.daysRemaining ?? 0;
    final iconData = _getBatteryIconForTimeframe(days);
    final iconColor = _getBatteryColorForTimeframe(days);

    if (days <= 1) {
      // Critical (24 hours): Fast blinking with red color
      return AnimatedBuilder(
        animation: _blinkAnimation,
        builder: (context, child) {
          return Opacity(
            opacity: _blinkAnimation.value,
            child: Icon(
              iconData,
              color: iconColor,
              size: widget.size,
            ),
          );
        },
      );
    } else if (days <= 7) {
      // Low (7 days): Slower blinking with orange color
      return AnimatedBuilder(
        animation: _blinkAnimation,
        builder: (context, child) {
          return Opacity(
            opacity: _blinkAnimation.value,
            child: Icon(
              iconData,
              color: iconColor,
              size: widget.size,
            ),
          );
        },
      );
    } else if (days <= 14) {
      // Medium (14 days): Gentle pulsing with yellow color
      return AnimatedBuilder(
        animation: _pulseAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _pulseAnimation.value,
            child: Icon(
              iconData,
              color: iconColor,
              size: widget.size,
            ),
          );
        },
      );
    }

    // For days > 14, return static icon with appropriate color
    return Icon(
      iconData,
      color: iconColor,
      size: widget.size,
    );
  }
}
