import 'package:sqflite/sqflite.dart';
import '../database/database_helper.dart';
import '../constants/database_constants.dart';
import '../utils/logger.dart';
import '../shared/enums/entry_enums.dart';
import '../di/service_locator.dart';
import '../../features/validation/domain/services/data_integrity_service.dart';

/// Service for migrating and synchronizing status and is_valid fields
class StatusFieldMigrationService {
  static final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// Run comprehensive status field synchronization
  static Future<void> runStatusFieldSynchronization() async {
    try {
      Logger.info(
          'StatusFieldMigrationService: Starting comprehensive status field synchronization');

      final db = await _databaseHelper.database;

      await db.transaction((txn) async {
        // Fix dismissed entry statuses first
        await _fixDismissedEntryStatuses(txn);

        // Synchronize meter readings
        await _synchronizeMeterReadingsStatus(txn);

        // Synchronize top-ups
        await _synchronizeTopUpsStatus(txn);

        // Create index on status field for performance
        await _createStatusIndexes(txn);
      });

      // Trigger validation recalculation after status fixes
      await _triggerValidationRecalculation();

      Logger.info(
          'StatusFieldMigrationService: Comprehensive status field synchronization completed successfully');
    } catch (e) {
      Logger.error(
          'StatusFieldMigrationService: Error during status field synchronization: $e');
      rethrow;
    }
  }

  /// Synchronize meter readings status and is_valid fields
  static Future<void> _synchronizeMeterReadingsStatus(Transaction txn) async {
    try {
      Logger.info(
          'StatusFieldMigrationService: Synchronizing meter readings status fields');

      // Get all meter readings
      final readings = await txn.query(DatabaseConstants.meterReadingsTable);

      int updatedCount = 0;

      for (final reading in readings) {
        final id = reading['id'] as int;
        final currentStatus = reading['status'] as int?;
        final currentIsValid = reading['is_valid'] as int?;

        // Determine correct values based on status field (primary)
        int correctStatus;
        int correctIsValid;

        if (currentStatus != null) {
          // Status field exists, use it as primary
          correctStatus = currentStatus;
          correctIsValid = _getIsValidFromStatus(currentStatus);
        } else if (currentIsValid != null) {
          // Fallback to is_valid field
          correctStatus = _getStatusFromIsValid(currentIsValid);
          correctIsValid = currentIsValid;
        } else {
          // Default to valid
          correctStatus = EntryStatus.valid.value;
          correctIsValid = 1;
        }

        // Update if values don't match
        if (currentStatus != correctStatus ||
            currentIsValid != correctIsValid) {
          await txn.update(
            DatabaseConstants.meterReadingsTable,
            {
              'status': correctStatus,
              'is_valid': correctIsValid,
              'updated_at': DateTime.now().toIso8601String(),
            },
            where: 'id = ?',
            whereArgs: [id],
          );
          updatedCount++;
        }
      }

      Logger.info(
          'StatusFieldMigrationService: Updated $updatedCount meter readings');
    } catch (e) {
      Logger.error(
          'StatusFieldMigrationService: Error synchronizing meter readings: $e');
      rethrow;
    }
  }

  /// Synchronize top-ups status and is_valid fields
  static Future<void> _synchronizeTopUpsStatus(Transaction txn) async {
    try {
      Logger.info(
          'StatusFieldMigrationService: Synchronizing top-ups status fields');

      // Get all top-ups
      final topUps = await txn.query(DatabaseConstants.topUpsTable);

      int updatedCount = 0;

      for (final topUp in topUps) {
        final id = topUp['id'] as int;
        final currentStatus = topUp['status'] as int?;
        final currentIsValid = topUp['is_valid'] as int?;

        // Determine correct values based on status field (primary)
        int correctStatus;
        int correctIsValid;

        if (currentStatus != null) {
          // Status field exists, use it as primary
          correctStatus = currentStatus;
          correctIsValid = _getIsValidFromStatus(currentStatus);
        } else if (currentIsValid != null) {
          // Fallback to is_valid field
          correctStatus = _getStatusFromIsValid(currentIsValid);
          correctIsValid = currentIsValid;
        } else {
          // Default to valid
          correctStatus = EntryStatus.valid.value;
          correctIsValid = 1;
        }

        // Update if values don't match
        if (currentStatus != correctStatus ||
            currentIsValid != correctIsValid) {
          await txn.update(
            DatabaseConstants.topUpsTable,
            {
              'status': correctStatus,
              'is_valid': correctIsValid,
              'updated_at': DateTime.now().toIso8601String(),
            },
            where: 'id = ?',
            whereArgs: [id],
          );
          updatedCount++;
        }
      }

      Logger.info('StatusFieldMigrationService: Updated $updatedCount top-ups');
    } catch (e) {
      Logger.error(
          'StatusFieldMigrationService: Error synchronizing top-ups: $e');
      rethrow;
    }
  }

  /// Create indexes on status fields for better performance
  static Future<void> _createStatusIndexes(Transaction txn) async {
    try {
      Logger.info('StatusFieldMigrationService: Creating status field indexes');

      // Create index on meter_readings status field
      await txn.execute('''
        CREATE INDEX IF NOT EXISTS idx_meter_readings_status
        ON ${DatabaseConstants.meterReadingsTable} (status)
      ''');

      // Create index on top_ups status field
      await txn.execute('''
        CREATE INDEX IF NOT EXISTS idx_top_ups_status
        ON ${DatabaseConstants.topUpsTable} (status)
      ''');

      Logger.info('StatusFieldMigrationService: Status field indexes created');
    } catch (e) {
      Logger.error(
          'StatusFieldMigrationService: Error creating status indexes: $e');
      rethrow;
    }
  }

  /// Convert status value to is_valid value
  static int _getIsValidFromStatus(int status) {
    switch (status) {
      case 0: // EntryStatus.valid
        return 1;
      case 1: // EntryStatus.invalid
        return 0;
      case 2: // EntryStatus.ignored
        return 0; // Dismissed entries are not "valid" in the traditional sense
      default:
        return 1; // Default to valid
    }
  }

  /// Convert is_valid value to status value
  static int _getStatusFromIsValid(int isValid) {
    return isValid == 1 ? EntryStatus.valid.value : EntryStatus.invalid.value;
  }

  /// Get migration statistics
  static Future<Map<String, dynamic>> getMigrationStatistics() async {
    try {
      final db = await _databaseHelper.database;

      // Count entries by status in meter_readings
      final meterReadingsStats = await db.rawQuery('''
        SELECT status, COUNT(*) as count 
        FROM ${DatabaseConstants.meterReadingsTable} 
        GROUP BY status
      ''');

      // Count entries by status in top_ups
      final topUpsStats = await db.rawQuery('''
        SELECT status, COUNT(*) as count 
        FROM ${DatabaseConstants.topUpsTable} 
        GROUP BY status
      ''');

      return {
        'meter_readings': meterReadingsStats,
        'top_ups': topUpsStats,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      Logger.error(
          'StatusFieldMigrationService: Error getting migration statistics: $e');
      return {
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// Fix existing dismissed entries to have correct status
  static Future<void> _fixDismissedEntryStatuses(Transaction txn) async {
    try {
      Logger.info(
          'StatusFieldMigrationService: Fixing dismissed entry statuses');

      // Find entries that look like dismissed entries but don't have correct status
      final entriesToFix = await txn.rawQuery('''
        SELECT id, status, value, notes
        FROM ${DatabaseConstants.meterReadingsTable}
        WHERE notes LIKE '%Dismissed missing entry gap%'
          AND value = 0.0
          AND status != 2
      ''');

      if (entriesToFix.isEmpty) {
        Logger.info(
            'StatusFieldMigrationService: No dismissed entries need status fixing');
        return;
      }

      Logger.info(
          'StatusFieldMigrationService: Found ${entriesToFix.length} dismissed entries to fix');

      // Fix each entry
      for (final entry in entriesToFix) {
        final id = entry['id'] as int;
        final currentStatus = entry['status'] as int?;
        final value = entry['value'] as double?;
        final notes = entry['notes'] as String?;

        Logger.info(
            'StatusFieldMigrationService: Fixing entry ID $id - Status: $currentStatus -> ${EntryStatus.ignored.value}, Value: $value');
        Logger.info('StatusFieldMigrationService: Entry notes: $notes');

        await txn.update(
          DatabaseConstants.meterReadingsTable,
          {
            'status': EntryStatus.ignored.value,
            'is_valid': 0,
            'updated_at': DateTime.now().toIso8601String(),
          },
          where: 'id = ?',
          whereArgs: [id],
        );

        Logger.info(
            'StatusFieldMigrationService: Successfully updated entry ID $id to status ${EntryStatus.ignored.value}');
      }

      Logger.info(
          'StatusFieldMigrationService: Fixed ${entriesToFix.length} dismissed entry statuses');
    } catch (e) {
      Logger.error(
          'StatusFieldMigrationService: Error fixing dismissed entry statuses: $e');
      rethrow;
    }
  }

  /// Trigger validation recalculation after migration
  static Future<void> _triggerValidationRecalculation() async {
    try {
      Logger.info(
          'StatusFieldMigrationService: Triggering validation recalculation');
      final dataIntegrityService = serviceLocator<DataIntegrityService>();
      await dataIntegrityService.validateAndUpdateAllMeterReadings();
      await dataIntegrityService.validateAndUpdateAllTopUps();
      Logger.info(
          'StatusFieldMigrationService: Validation recalculation completed');
    } catch (e) {
      Logger.error(
          'StatusFieldMigrationService: Error during validation recalculation: $e');
    }
  }
}
