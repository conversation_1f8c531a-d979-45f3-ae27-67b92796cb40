// File: lib/features/validation/domain/services/dismissed_entry_service.dart
import '../../../../core/utils/logger.dart';
import '../../../../core/shared/enums/entry_enums.dart';
import '../../../meter_readings/domain/models/meter_reading.dart';
import '../models/validation_issue.dart';
import 'simple_gap_detection_service.dart';

/// Simple service for managing dismissed gaps (no more fake entries)
class DismissedEntryService {
  final SimpleGapDetectionService _gapDetectionService;

  const DismissedEntryService(this._gapDetectionService);

  /// Dismiss a missing entry gap by creating a Records Gap entry
  Future<void> createDismissalEntry(ValidationIssue issue) async {
    try {
      Logger.info(
          'DismissedEntryService: Creating Records Gap entry for dismissed gap');
      await _gapDetectionService.dismissGap(issue);
      Logger.info(
          'DismissedEntryService: Records Gap entry created successfully');
    } catch (e) {
      Logger.error(
          'DismissedEntryService: Error creating Records Gap entry: $e');
      rethrow;
    }
  }

  /// Check if a gap period has been dismissed by looking for record gap entries
  Future<bool> isGapDismissed(
      List<dynamic> readings, DateTime start, DateTime end) async {
    try {
      // Check if any record gap entry exists within the gap period
      for (final reading in readings) {
        if (reading is MeterReading) {
          if (reading.status == EntryStatus.ignored &&
              reading.notes?.contains('Records Gap:') == true &&
              reading.date.isAfter(start) &&
              reading.date.isBefore(end)) {
            return true;
          }
        }
      }
      return false;
    } catch (e) {
      Logger.error(
          'DismissedEntryService: Error checking if gap is dismissed: $e');
      return false;
    }
  }

  /// Check if an entry is a dismissed entry (legacy method - always returns false now)
  bool isDismissedEntry(dynamic reading) {
    // No more dismissed entries in the database - they're stored as gap metadata
    return false;
  }

  /// Clean up invalid dismissal entries (legacy method - does nothing now)
  Future<void> cleanupInvalidDismissalEntries() async {
    Logger.info('DismissedEntryService: No cleanup needed with new system');
    // The migration already cleaned up old dismissed entries
  }
}
