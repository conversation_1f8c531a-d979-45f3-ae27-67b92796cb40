/// Notification types
enum NotificationType {
  /// Low balance alert
  lowBalance,

  /// Time to top-up alert
  timeToTopUp,

  /// Invalid record alert
  invalidRecord,

  /// Reading reminder
  readingReminder,

  /// Welcome message
  welcome,

  /// App update available
  appUpdate,
}

/// A notification in the app
class AppNotification {
  /// Unique identifier
  final int? id;

  /// Notification title
  final String title;

  /// Notification message
  final String message;

  /// Timestamp when the notification was created
  final DateTime timestamp;

  /// Type of notification
  final NotificationType type;

  /// Whether the notification has been read
  final bool isRead;

  /// Constructor
  const AppNotification({
    this.id,
    required this.title,
    required this.message,
    required this.timestamp,
    required this.type,
    this.isRead = false,
  });

  /// Create a copy of this notification with some fields changed
  AppNotification copyWith({
    int? id,
    String? title,
    String? message,
    DateTime? timestamp,
    NotificationType? type,
    bool? isRead,
  }) {
    return AppNotification(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      timestamp: timestamp ?? this.timestamp,
      type: type ?? this.type,
      isRead: isRead ?? this.isRead,
    );
  }

  /// Create a notification from a map
  factory AppNotification.fromMap(Map<String, dynamic> map) {
    return AppNotification(
      id: map['id'] as int?,
      title: map['title'] as String,
      message: map['message'] as String,
      timestamp: DateTime.parse(map['timestamp'] as String),
      type: NotificationType.values[map['type'] as int],
      isRead: (map['isRead'] as int) == 1,
    );
  }
}

/// Extension methods for AppNotification
extension AppNotificationX on AppNotification {
  /// Convert notification to a map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'message': message,
      'timestamp': timestamp.toIso8601String(),
      'type': type.index,
      'isRead': isRead ? 1 : 0,
    };
  }

  /// Get the priority level of the notification
  int get priorityLevel {
    switch (type) {
      case NotificationType.lowBalance:
        return 3; // High priority
      case NotificationType.timeToTopUp:
      case NotificationType.invalidRecord:
        return 2; // Medium priority
      case NotificationType.readingReminder:
      case NotificationType.welcome:
      case NotificationType.appUpdate:
        return 1; // Normal priority
    }
  }

  /// Get the icon for the notification
  String get iconName {
    switch (type) {
      case NotificationType.lowBalance:
        return 'warning';
      case NotificationType.timeToTopUp:
        return 'add_card';
      case NotificationType.invalidRecord:
        return 'error';
      case NotificationType.readingReminder:
        return 'schedule';
      case NotificationType.welcome:
        return 'info';
      case NotificationType.appUpdate:
        return 'update';
    }
  }
}
