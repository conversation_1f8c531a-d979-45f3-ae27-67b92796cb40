import 'package:package_info_plus/package_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logger.dart';
import '../../features/notifications/domain/models/notification.dart';
import '../../features/notifications/data/notification_service.dart';
import '../di/service_locator.dart';

/// Service for checking app updates
class AppUpdateService {
  static const String _lastUpdateCheckKey = 'last_update_check_date';
  static const String _lastNotifiedVersionKey = 'last_notified_version';

  /// Check for app updates manually
  Future<AppUpdateResult> checkForUpdates() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      final currentVersion = packageInfo.version;

      // During development: placeholder for GitHub releases check
      // After release: placeholder for app store check
      
      // For now, return no updates available
      await _setLastUpdateCheckDate();
      
      return AppUpdateResult(
        hasUpdate: false,
        currentVersion: currentVersion,
        latestVersion: currentVersion,
        updateUrl: null,
      );
    } catch (e) {
      Logger.error('Error checking for updates: $e');
      return AppUpdateResult(
        hasUpdate: false,
        currentVersion: 'Unknown',
        latestVersion: 'Unknown',
        updateUrl: null,
        error: e.toString(),
      );
    }
  }

  /// Create update notification (for future push notification integration)
  Future<void> createUpdateNotification(String newVersion) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastNotifiedVersion = prefs.getString(_lastNotifiedVersionKey);

      // Don't notify for the same version twice
      if (lastNotifiedVersion == newVersion) return;

      final notification = AppNotification(
        title: 'App Update Available',
        message: 'Lekky v$newVersion is now available. Update for the latest features and improvements.',
        timestamp: DateTime.now(),
        type: NotificationType.appUpdate,
      );

      final notificationService = await serviceLocator.getAsync<NotificationService>();
      await notificationService.showNotification(notification);

      // Track that we've notified for this version
      await prefs.setString(_lastNotifiedVersionKey, newVersion);
      
      Logger.info('Update notification created for version $newVersion');
    } catch (e) {
      Logger.error('Error creating update notification: $e');
    }
  }

  /// Get last update check date
  Future<DateTime?> getLastUpdateCheckDate() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final dateString = prefs.getString(_lastUpdateCheckKey);
      return dateString != null ? DateTime.parse(dateString) : null;
    } catch (e) {
      Logger.error('Error getting last update check date: $e');
      return null;
    }
  }

  /// Set last update check date
  Future<void> _setLastUpdateCheckDate() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastUpdateCheckKey, DateTime.now().toIso8601String());
    } catch (e) {
      Logger.error('Error setting last update check date: $e');
    }
  }

  /// Check if it's time for weekly update notification
  Future<bool> shouldCheckForWeeklyNotification() async {
    try {
      final lastCheck = await getLastUpdateCheckDate();
      if (lastCheck == null) return true;

      final daysSinceLastCheck = DateTime.now().difference(lastCheck).inDays;
      return daysSinceLastCheck >= 7; // Weekly check
    } catch (e) {
      Logger.error('Error checking weekly notification timing: $e');
      return false;
    }
  }
}

/// Result of app update check
class AppUpdateResult {
  final bool hasUpdate;
  final String currentVersion;
  final String latestVersion;
  final String? updateUrl;
  final String? error;

  const AppUpdateResult({
    required this.hasUpdate,
    required this.currentVersion,
    required this.latestVersion,
    this.updateUrl,
    this.error,
  });

  bool get isSuccess => error == null;
}
