// File: lib/features/validation/domain/utils/validation_message_builder.dart
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/constants/preference_keys.dart';
import '../../../meter_readings/domain/models/meter_reading.dart';
import '../../../top_ups/domain/models/top_up.dart';
import '../models/validation_issue.dart';

/// Centralized builder for validation messages with consistent formatting
class ValidationMessageBuilder {
  static final _logger = Logger('ValidationMessageBuilder');

  /// Generate detailed validation message for balance inconsistency
  static Future<String> buildBalanceInconsistencyMessage({
    required MeterReading reading,
    double? expectedValue,
  }) async {
    final currencySymbol = await _getCurrencySymbol();
    final dateStr = _formatDateForDisplay(reading.date);

    if (expectedValue != null) {
      return 'Invalid reading! Your meter reading on $dateStr of '
          '$currencySymbol${reading.value.toStringAsFixed(2)} seems too high.\n\n'
          '• Should be less than $currencySymbol${expectedValue.toStringAsFixed(2)}\n'
          '• Check entries before and after this reading\n'
          '• Look for wrong dates or typos in amounts';
    } else {
      return 'Invalid reading! Your meter reading on $dateStr of '
          '$currencySymbol${reading.value.toStringAsFixed(2)} doesn\'t match your usage pattern.\n\n'
          '• Double-check this entry and surrounding ones\n'
          '• Look for entries in wrong order\n'
          '• Check for incorrect dates or amount typos';
    }
  }

  /// Generate message for chronological order issues
  static Future<String> buildChronologicalOrderMessage({
    required MeterReading current,
    required DateTime previousDate,
  }) async {
    final currencySymbol = await _getCurrencySymbol();
    final currentDateStr = _formatDateForDisplay(current.date);
    final previousDateStr = _formatDateForDisplay(previousDate);

    return 'Date order problem! Your meter reading on $currentDateStr of '
        '$currencySymbol${current.value.toStringAsFixed(2)} is dated before the previous entry on $previousDateStr.\n\n'
        '• Check if you entered the wrong date or time\n'
        '• Entries should be in chronological order\n'
        '• Newer entries need later dates than older ones';
  }

  /// Generate message for negative meter reading values
  static Future<String> buildNegativeMeterReadingMessage({
    required MeterReading reading,
  }) async {
    final currencySymbol = await _getCurrencySymbol();
    final dateStr = _formatDateForDisplay(reading.date);

    return 'Invalid negative meter reading! Your meter reading on $dateStr of '
        '$currencySymbol${reading.value.toStringAsFixed(2)} cannot be negative.\n\n'
        '• Meter readings should always be positive\n'
        '• They represent your remaining credit balance\n'
        '• Check if you entered the wrong amount';
  }

  /// Generate message for negative top-up amounts
  static Future<String> buildNegativeTopUpMessage({
    required TopUp topUp,
  }) async {
    final currencySymbol = await _getCurrencySymbol();
    final dateStr = _formatDateForDisplay(topUp.date);

    return 'Invalid negative top-up! Your top-up on $dateStr of '
        '$currencySymbol${topUp.amount.toStringAsFixed(2)} cannot be negative.\n\n'
        '• Top-ups should always be positive amounts\n'
        '• They represent credit you added to your meter\n'
        '• Check if you entered the wrong amount';
  }

  /// Generate message for future date issues in meter readings
  static Future<String> buildFutureDateMeterReadingMessage({
    required MeterReading reading,
  }) async {
    final currencySymbol = await _getCurrencySymbol();
    final dateStr = _formatDateForDisplay(reading.date);

    return 'Future date detected! Your meter reading on $dateStr of '
        '$currencySymbol${reading.value.toStringAsFixed(2)} is dated in the future.\n\n'
        '• Check if you entered the wrong date or time\n'
        '• Entries should be recorded when you took the reading\n'
        '• Not scheduled for later';
  }

  /// Generate message for future date issues in top-ups
  static Future<String> buildFutureDateTopUpMessage({
    required TopUp topUp,
  }) async {
    final currencySymbol = await _getCurrencySymbol();
    final dateStr = _formatDateForDisplay(topUp.date);

    return 'Future date detected! Your top-up on $dateStr of '
        '$currencySymbol${topUp.amount.toStringAsFixed(2)} is dated in the future.\n\n'
        '• Check if you entered the wrong date or time\n'
        '• Top-ups should be recorded when you added credit\n'
        '• Not scheduled for later';
  }

  /// Generate message for duplicate meter readings
  static Future<String> buildDuplicateMeterReadingMessage({
    required MeterReading reading,
    required int duplicateCount,
  }) async {
    final currencySymbol = await _getCurrencySymbol();
    final dateStr = _formatDateForDisplay(reading.date);

    return 'Duplicate meter reading found! Your meter reading on $dateStr of '
        '$currencySymbol${reading.value.toStringAsFixed(2)} is one of $duplicateCount readings for the same date.\n\n'
        '• Check if you entered the same reading twice\n'
        '• One might have the wrong date\n'
        '• Keep the correct one and delete/fix others';
  }

  /// Generate message for duplicate top-ups
  static Future<String> buildDuplicateTopUpMessage({
    required TopUp topUp,
    required int duplicateCount,
  }) async {
    final currencySymbol = await _getCurrencySymbol();
    final dateStr = _formatDateForDisplay(topUp.date);

    return 'Duplicate top-up found! Your top-up on $dateStr of '
        '$currencySymbol${topUp.amount.toStringAsFixed(2)} is one of $duplicateCount top-ups for the same date.\n\n'
        '• This might be okay if you topped up multiple times\n'
        '• Check if you entered the same top-up twice\n'
        '• One might have the wrong date';
  }

  /// Generate message for missing entries (large gaps)
  static String buildMissingEntryMessage({
    required int gapDays,
    DateTime? startDate,
    DateTime? endDate,
  }) {
    String gapDescription;
    if (startDate != null && endDate != null) {
      final startDateStr = _formatDateForDisplay(startDate);
      final endDateStr = _formatDateForDisplay(endDate);
      gapDescription =
          'Large gap detected! You have a $gapDays-day gap between entry dates $startDateStr and $endDateStr.';
    } else {
      gapDescription =
          'Large gap detected! You have a $gapDays-day gap between entries.';
    }

    return '$gapDescription\n\n'
        '• This might be normal if you were away\n'
        '• Check if you forgot to record any readings\n'
        '• Look for missing top-ups during this period';
  }

  /// Get user-friendly issue type text
  static String getIssueTypeText(ValidationIssueType type) {
    switch (type) {
      case ValidationIssueType.negativeValue:
        return 'Negative Value';
      case ValidationIssueType.futureDate:
        return 'Future Date';
      case ValidationIssueType.chronologicalOrder:
        return 'Chronological Order';
      case ValidationIssueType.balanceInconsistency:
        return 'Balance Inconsistency';
      case ValidationIssueType.duplicateEntry:
        return 'Duplicate Entry';
      case ValidationIssueType.missingEntry:
        return 'Missing Entry';
      case ValidationIssueType.other:
        return 'Other Issue';
    }
  }

  /// Format a date for user-friendly display
  static String _formatDateForDisplay(DateTime date) {
    final months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }

  /// Get the user's currency symbol from preferences
  static Future<String> _getCurrencySymbol() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(PreferenceKeys.currencySymbol) ?? '£';
    } catch (e) {
      _logger.e('Failed to get currency symbol: $e');
      return '£'; // Default fallback
    }
  }
}
