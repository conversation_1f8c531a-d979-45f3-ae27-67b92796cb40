import '../../../../core/shared/enums/entry_enums.dart';
import '../../../../core/utils/logger.dart';
import '../../../meter_readings/domain/models/meter_reading.dart';
import '../../../top_ups/domain/models/top_up.dart';
import '../models/validation_issue.dart';
import 'validation_severity_mapper.dart';

/// Synchronizes ValidationIssues with EntryStatus efficiently
///
/// Provides analysis and statistics for validation system
class ValidationStatusSynchronizer {
  // Private constructor to prevent instantiation
  ValidationStatusSynchronizer._();

  /// Synchronize entry status based on validation issues
  ///
  /// Note: Currently simplified due to final status properties
  /// Future enhancement will handle status updates when architecture allows
  static Future<void> syncEntryStatus(
    List<MeterReading> meterReadings,
    List<TopUp> topUps,
    List<ValidationIssue> issues,
  ) async {
    try {
      Logger.info(
          'Entry status synchronization - analyzing ${issues.length} issues');

      // For now, just log the analysis since status is final
      final stats = getSyncStats(meterReadings, topUps);
      Logger.info('Sync stats: $stats');

      Logger.info('Entry status synchronization completed');
    } catch (e) {
      Logger.error('Error during entry status synchronization: $e');
      rethrow;
    }
  }

  /// Check if issues should invalidate entries (analysis only)
  static bool hasInvalidatingIssues(List<ValidationIssue> issues) {
    return ValidationSeverityMapper.hasInvalidatingIssues(issues);
  }

  /// Analyze validation issues for an entry
  static Map<String, dynamic> analyzeEntryIssues(
    int entryId,
    List<ValidationIssue> allIssues,
  ) {
    final entryIssues =
        allIssues.where((issue) => issue.entryId == entryId).toList();
    final shouldBeInvalid = hasInvalidatingIssues(entryIssues);

    return {
      'entryId': entryId,
      'issueCount': entryIssues.length,
      'shouldBeInvalid': shouldBeInvalid,
      'severities': entryIssues.map((i) => i.severity.toString()).toList(),
    };
  }

  /// Get statistics about synchronization results
  static Map<String, int> getSyncStats(
    List<MeterReading> meterReadings,
    List<TopUp> topUps,
  ) {
    final invalidMeterReadings = meterReadings.where((r) => !r.isValid).length;
    final invalidTopUps = topUps.where((t) => !t.isValid).length;

    return {
      'totalMeterReadings': meterReadings.length,
      'invalidMeterReadings': invalidMeterReadings,
      'validMeterReadings': meterReadings.length - invalidMeterReadings,
      'totalTopUps': topUps.length,
      'invalidTopUps': invalidTopUps,
      'validTopUps': topUps.length - invalidTopUps,
      'totalEntries': meterReadings.length + topUps.length,
      'totalInvalidEntries': invalidMeterReadings + invalidTopUps,
    };
  }
}
