// File: lib/core/providers/settings_navigation_provider.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Provider for managing settings screen navigation state
/// Preserves expanded category state during navigation
class SettingsNavigationState {
  /// Currently expanded category index (-1 means no category expanded)
  final int expandedCategoryIndex;
  
  /// Timestamp when state was last updated
  final DateTime lastUpdated;

  const SettingsNavigationState({
    required this.expandedCategoryIndex,
    required this.lastUpdated,
  });

  /// Create initial state with no expanded category
  factory SettingsNavigationState.initial() {
    return SettingsNavigationState(
      expandedCategoryIndex: -1,
      lastUpdated: DateTime.now(),
    );
  }

  /// Copy with new values
  SettingsNavigationState copyWith({
    int? expandedCategoryIndex,
    DateTime? lastUpdated,
  }) {
    return SettingsNavigationState(
      expandedCategoryIndex: expandedCategoryIndex ?? this.expandedCategoryIndex,
      lastUpdated: lastUpdated ?? DateTime.now(),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SettingsNavigationState &&
        other.expandedCategoryIndex == expandedCategoryIndex;
  }

  @override
  int get hashCode => expandedCategoryIndex.hashCode;
}

/// StateNotifier for managing settings navigation state
class SettingsNavigationNotifier extends StateNotifier<SettingsNavigationState> {
  SettingsNavigationNotifier() : super(SettingsNavigationState.initial());

  /// Set the expanded category index
  void setExpandedCategory(int categoryIndex) {
    state = state.copyWith(expandedCategoryIndex: categoryIndex);
  }

  /// Clear expanded category (collapse all)
  void clearExpandedCategory() {
    state = state.copyWith(expandedCategoryIndex: -1);
  }

  /// Toggle category expansion
  void toggleCategory(int categoryIndex) {
    if (state.expandedCategoryIndex == categoryIndex) {
      clearExpandedCategory();
    } else {
      setExpandedCategory(categoryIndex);
    }
  }
}

/// Provider for settings navigation state management
final settingsNavigationProvider = StateNotifierProvider<SettingsNavigationNotifier, SettingsNavigationState>((ref) {
  return SettingsNavigationNotifier();
});

/// Category index constants for better maintainability
class SettingsCategoryIndex {
  static const int region = 0;
  static const int notifications = 1;
  static const int dateSettings = 2;
  static const int appearance = 3;
  static const int dataBackup = 4;
  static const int about = 5;
  static const int donate = 6;
  static const int testing = 7;
}
