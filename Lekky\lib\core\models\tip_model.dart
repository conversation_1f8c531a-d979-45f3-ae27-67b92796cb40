import 'package:flutter/material.dart';

/// Platform filter for tips
enum TipPlatform {
  all,
  android,
  ios,
  androidLegacy, // Android 5.0-8.0
  androidModern, // Android 9.0+
}

/// Tip category for organization
enum TipCategory {
  general,
  notifications,
  permissions,
  troubleshooting,
  importExport,
}

/// Individual tip model
class TipModel {
  final String id;
  final String text;
  final IconData icon;
  final Color color;
  final TipCategory category;
  final Set<TipPlatform> platforms;
  final int? minAndroidApi;
  final int? maxAndroidApi;

  const TipModel({
    required this.id,
    required this.text,
    required this.icon,
    required this.color,
    required this.category,
    this.platforms = const {TipPlatform.all},
    this.minAndroidApi,
    this.maxAndroidApi,
  });

  /// Check if tip should be shown for given platform info
  bool shouldShowForPlatform({
    required bool isAndroid,
    required bool isIOS,
    int? androidSdkInt,
  }) {
    // Check platform compatibility
    if (platforms.contains(TipPlatform.all)) return true;
    
    if (isIOS && platforms.contains(TipPlatform.ios)) return true;
    
    if (isAndroid) {
      if (platforms.contains(TipPlatform.android)) return true;
      
      if (androidSdkInt != null) {
        // Check Android version-specific platforms
        if (platforms.contains(TipPlatform.androidLegacy) && androidSdkInt <= 28) {
          return true;
        }
        if (platforms.contains(TipPlatform.androidModern) && androidSdkInt >= 29) {
          return true;
        }
        
        // Check API level ranges
        if (minAndroidApi != null && androidSdkInt < minAndroidApi!) {
          return false;
        }
        if (maxAndroidApi != null && androidSdkInt > maxAndroidApi!) {
          return false;
        }
      }
    }
    
    return false;
  }
}

/// Tips data repository
class TipsRepository {
  static const List<TipModel> _allTips = [
    // General tips
    TipModel(
      id: 'consistent_readings',
      text: 'Enter readings at consistent times for more accurate averages',
      icon: Icons.schedule,
      color: Colors.amber,
      category: TipCategory.general,
    ),
    TipModel(
      id: 'usage_patterns',
      text: 'Use the history screen to identify usage patterns',
      icon: Icons.analytics,
      color: Colors.amber,
      category: TipCategory.general,
    ),
    TipModel(
      id: 'regular_topups',
      text: 'Regular top-ups help maintain a steady credit balance',
      icon: Icons.account_balance_wallet,
      color: Colors.amber,
      category: TipCategory.general,
    ),
    TipModel(
      id: 'cost_monitoring',
      text: 'Check the cost screen to monitor your spending',
      icon: Icons.trending_up,
      color: Colors.amber,
      category: TipCategory.general,
    ),
    
    // Import/Export - Android Legacy
    TipModel(
      id: 'export_android_legacy',
      text: 'On older Android versions, exported files are saved to app storage. Use your file manager to find them under Android/data/com.lekky.app/files/',
      icon: Icons.folder,
      color: Colors.blue,
      category: TipCategory.importExport,
      platforms: {TipPlatform.androidLegacy},
      maxAndroidApi: 28,
    ),
    TipModel(
      id: 'import_android_legacy',
      text: 'For importing on older Android, place CSV files in Downloads folder or use the file picker to browse to any location',
      icon: Icons.file_upload,
      color: Colors.blue,
      category: TipCategory.importExport,
      platforms: {TipPlatform.androidLegacy},
      maxAndroidApi: 28,
    ),
    
    // Import/Export - Android Modern
    TipModel(
      id: 'export_android_modern',
      text: 'Exported files are automatically saved to your Downloads folder for easy access',
      icon: Icons.download,
      color: Colors.blue,
      category: TipCategory.importExport,
      platforms: {TipPlatform.androidModern},
      minAndroidApi: 29,
    ),
    TipModel(
      id: 'import_android_modern',
      text: 'Import CSV files from Downloads folder or use the file picker to choose any location on your device',
      icon: Icons.file_upload,
      color: Colors.blue,
      category: TipCategory.importExport,
      platforms: {TipPlatform.androidModern},
      minAndroidApi: 29,
    ),
    
    // Import/Export - iOS
    TipModel(
      id: 'export_ios',
      text: 'Use the Files app to manage your exported CSV files. You can save them to iCloud Drive or other cloud storage',
      icon: Icons.cloud,
      color: Colors.blue,
      category: TipCategory.importExport,
      platforms: {TipPlatform.ios},
    ),
    TipModel(
      id: 'import_ios',
      text: 'Import CSV files from the Files app, iCloud Drive, or other cloud storage services',
      icon: Icons.file_upload,
      color: Colors.blue,
      category: TipCategory.importExport,
      platforms: {TipPlatform.ios},
    ),
    
    // Large dataset tip
    TipModel(
      id: 'large_dataset',
      text: 'Large exports (200+ entries) may take longer depending on your device. The app will show progress during the operation',
      icon: Icons.hourglass_empty,
      color: Colors.orange,
      category: TipCategory.importExport,
    ),
    
    // Notifications
    TipModel(
      id: 'notification_setup',
      text: 'Set up notifications to stay on top of your usage',
      icon: Icons.notifications_active,
      color: Colors.amber,
      category: TipCategory.notifications,
    ),
    TipModel(
      id: 'notification_bell',
      text: 'Tap the notification bell icon on the Dashboard to view all notifications',
      icon: Icons.notifications,
      color: Colors.orange,
      category: TipCategory.notifications,
    ),
    TipModel(
      id: 'notification_config',
      text: 'Configure notification types in Settings → Alerts & Notifications',
      icon: Icons.settings,
      color: Colors.orange,
      category: TipCategory.notifications,
    ),
    TipModel(
      id: 'meter_reminders',
      text: 'Set up reminders to check your meter regularly',
      icon: Icons.schedule,
      color: Colors.orange,
      category: TipCategory.notifications,
    ),
    TipModel(
      id: 'low_balance_alerts',
      text: 'Enable low balance alerts to avoid running out of credit',
      icon: Icons.warning,
      color: Colors.orange,
      category: TipCategory.notifications,
    ),
    TipModel(
      id: 'notification_utilities',
      text: 'Use Utilities in Alerts & Notifications to manage permissions and reset notification data',
      icon: Icons.build,
      color: Colors.orange,
      category: TipCategory.notifications,
    ),
    TipModel(
      id: 'reset_alerts',
      text: 'Use "Reset All Alert States" in Utilities to allow alerts to fire again immediately if conditions are met',
      icon: Icons.refresh,
      color: Colors.orange,
      category: TipCategory.notifications,
    ),
    TipModel(
      id: 'notification_debug',
      text: 'Access Testing → Notification Debug for advanced troubleshooting and system status',
      icon: Icons.bug_report,
      color: Colors.orange,
      category: TipCategory.notifications,
    ),
    
    // Android-specific notification tips
    TipModel(
      id: 'android_notification_permission',
      text: 'On Android 13+, ensure "Allow notifications" is enabled in your device settings for Lekky',
      icon: Icons.phone_android,
      color: Colors.orange,
      category: TipCategory.notifications,
      platforms: {TipPlatform.android},
      minAndroidApi: 33,
    ),
    
    // Troubleshooting
    TipModel(
      id: 'meter_reminder_timing',
      text: 'Meter reminders won\'t fire if you\'ve already taken a reading after 3 AM the same day',
      icon: Icons.schedule,
      color: Colors.red,
      category: TipCategory.troubleshooting,
    ),
    TipModel(
      id: 'notification_troubleshooting',
      text: 'Notifications acting up? Use "Clear All Notification Data" in Utilities to reset the system',
      icon: Icons.refresh,
      color: Colors.red,
      category: TipCategory.troubleshooting,
    ),
    TipModel(
      id: 'diagnostic_report',
      text: 'For detailed diagnostics, go to Settings → Testing → Notification Debug → Generate Status Report',
      icon: Icons.bug_report,
      color: Colors.red,
      category: TipCategory.troubleshooting,
    ),
    TipModel(
      id: 'independent_alerts',
      text: 'Individual alert types work independently - you can enable Low Balance alerts without the main toggle',
      icon: Icons.toggle_on,
      color: Colors.red,
      category: TipCategory.troubleshooting,
    ),
  ];

  /// Get all tips
  static List<TipModel> getAllTips() => _allTips;

  /// Get tips filtered by platform
  static List<TipModel> getTipsForPlatform({
    required bool isAndroid,
    required bool isIOS,
    int? androidSdkInt,
  }) {
    return _allTips.where((tip) => tip.shouldShowForPlatform(
      isAndroid: isAndroid,
      isIOS: isIOS,
      androidSdkInt: androidSdkInt,
    )).toList();
  }

  /// Get tips by category
  static List<TipModel> getTipsByCategory(TipCategory category) {
    return _allTips.where((tip) => tip.category == category).toList();
  }

  /// Get tips by category and platform
  static List<TipModel> getTipsByCategoryAndPlatform({
    required TipCategory category,
    required bool isAndroid,
    required bool isIOS,
    int? androidSdkInt,
  }) {
    return _allTips.where((tip) => 
      tip.category == category && 
      tip.shouldShowForPlatform(
        isAndroid: isAndroid,
        isIOS: isIOS,
        androidSdkInt: androidSdkInt,
      )
    ).toList();
  }
}
