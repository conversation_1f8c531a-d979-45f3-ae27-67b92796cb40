import '../../features/meter_readings/domain/models/meter_reading.dart';
import '../shared/enums/entry_enums.dart';

/// Unified utility for detecting records gap entries throughout the codebase
/// Single source of truth for records gap entry detection logic
class DismissedEntryDetector {
  // Private constructor to prevent instantiation
  DismissedEntryDetector._();

  /// Check if a meter reading is a records gap entry
  /// Uses fallback detection: status == EntryStatus.ignored OR notes contains 'Records Gap:'
  static bool isDismissedEntry(MeterReading reading) {
    return reading.status == EntryStatus.ignored ||
        (reading.notes?.contains('Records Gap:') == true);
  }

  /// Check if an entry matches records gap criteria (for database inspection)
  /// Uses simplified criteria: ONLY status == 2 (ignored)
  static bool isDismissedEntryFromStatus(int? status) {
    return status == 2; // EntryStatus.ignored.value
  }
}
