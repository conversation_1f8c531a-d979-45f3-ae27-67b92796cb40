// File: lib/features/validation/data/repositories/dismissed_gap_repository_impl.dart

import 'package:sqflite/sqflite.dart';
import '../../../../core/database/database_helper.dart';
import '../../../../core/constants/database_constants.dart';
import '../../../../core/utils/logger.dart';
import '../../domain/models/dismissed_gap.dart';
import '../../domain/repositories/dismissed_gap_repository.dart';

/// SQLite implementation of dismissed gap repository
class DismissedGapRepositoryImpl implements DismissedGapRepository {
  final DatabaseHelper _databaseHelper;
  static const String _tableName = DatabaseConstants.dismissedGapsTable;

  /// Constructor
  DismissedGapRepositoryImpl(this._databaseHelper);

  /// Get database instance
  Future<Database> get _database async => await _databaseHelper.database;

  @override
  Future<int> addDismissedGap(DismissedGap gap) async {
    try {
      final db = await _database;
      final id = await db.insert(_tableName, gap.toMap());
      Logger.info('DismissedGapRepository: Added dismissed gap with ID $id');
      return id;
    } catch (e) {
      Logger.error('DismissedGapRepository: Error adding dismissed gap: $e');
      rethrow;
    }
  }

  @override
  Future<List<DismissedGap>> getAllDismissedGaps() async {
    try {
      final db = await _database;
      final maps = await db.query(_tableName, orderBy: 'start_date ASC');
      return maps.map((map) => DismissedGap.fromMap(map)).toList();
    } catch (e) {
      Logger.error(
          'DismissedGapRepository: Error getting all dismissed gaps: $e');
      return [];
    }
  }

  @override
  Future<List<DismissedGap>> getDismissedGapsInRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final db = await _database;
      final maps = await db.query(
        _tableName,
        where: 'start_date <= ? AND end_date >= ?',
        whereArgs: [endDate.toIso8601String(), startDate.toIso8601String()],
        orderBy: 'start_date ASC',
      );
      return maps.map((map) => DismissedGap.fromMap(map)).toList();
    } catch (e) {
      Logger.error(
          'DismissedGapRepository: Error getting dismissed gaps in range: $e');
      return [];
    }
  }

  @override
  Future<void> deleteDismissedGap(int id) async {
    try {
      final db = await _database;
      await db.delete(_tableName, where: 'id = ?', whereArgs: [id]);
      Logger.info('DismissedGapRepository: Deleted dismissed gap with ID $id');
    } catch (e) {
      Logger.error('DismissedGapRepository: Error deleting dismissed gap: $e');
      rethrow;
    }
  }

  @override
  Future<void> deleteAllDismissedGaps() async {
    try {
      final db = await _database;
      await db.delete(_tableName);
      Logger.info('DismissedGapRepository: Deleted all dismissed gaps');
    } catch (e) {
      Logger.error(
          'DismissedGapRepository: Error deleting all dismissed gaps: $e');
      rethrow;
    }
  }

  @override
  Future<bool> isGapDismissed(DateTime gapStart, DateTime gapEnd) async {
    try {
      Logger.info(
          'DismissedGapRepository: Checking if gap is dismissed from $gapStart to $gapEnd');
      final dismissedGaps = await getDismissedGapsInRange(gapStart, gapEnd);
      Logger.info(
          'DismissedGapRepository: Found ${dismissedGaps.length} dismissed gaps in range');

      // Check if any dismissed gap overlaps with this gap
      for (final dismissedGap in dismissedGaps) {
        if (dismissedGap.overlapsWithGap(gapStart, gapEnd)) {
          Logger.info(
              'DismissedGapRepository: Gap is dismissed by record: ${dismissedGap.startDate} to ${dismissedGap.endDate}');
          return true;
        }
      }

      Logger.info('DismissedGapRepository: Gap is NOT dismissed');
      return false;
    } catch (e) {
      Logger.error(
          'DismissedGapRepository: Error checking if gap is dismissed: $e');
      return false;
    }
  }
}
