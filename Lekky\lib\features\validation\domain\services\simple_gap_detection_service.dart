// File: lib/features/validation/domain/services/simple_gap_detection_service.dart

import '../../../../core/utils/logger.dart';
import '../../../../core/shared/enums/entry_enums.dart';
import '../../../meter_readings/domain/models/meter_reading.dart';
import '../../../meter_readings/domain/repositories/meter_reading_repository.dart';
import '../../../top_ups/domain/models/top_up.dart';
import '../models/validation_issue.dart';
import '../utils/validation_message_builder.dart';

/// Simple, clean gap detection service
class SimpleGapDetectionService {
  final MeterReadingRepository _meterReadingRepository;

  const SimpleGapDetectionService(this._meterReadingRepository);

  /// Find all missing entry gaps (> 62 days) that don't have record gap entries
  Future<List<ValidationIssue>> findMissingEntryGaps(
    List<MeterReading> meterReadings,
    List<TopUp> topUps,
  ) async {
    try {
      print('=== GAP DETECTION STARTING ===');
      Logger.info('SimpleGapDetectionService: Starting gap detection');
      print('Meter readings count: ${meterReadings.length}');
      print('Top-ups count: ${topUps.length}');

      // Get only real entries (exclude record gap entries) for gap detection
      final realEntryDates = <DateTime>{};

      // Add only real meter readings (exclude record gap entries)
      for (final reading in meterReadings) {
        if (!_isRecordGapEntry(reading)) {
          realEntryDates.add(reading.date);
        }
      }

      // Add all top-ups
      for (final topUp in topUps) {
        realEntryDates.add(topUp.date);
      }

      // Convert to sorted list of unique dates
      final realEntries = realEntryDates.toList()..sort();

      if (realEntries.length < 2) {
        Logger.info(
            'SimpleGapDetectionService: Not enough real entries for gap detection');
        return [];
      }

      final issues = <ValidationIssue>[];

      Logger.info(
          'SimpleGapDetectionService: Real entry dates: ${realEntries.map((d) => d.toString().substring(0, 10)).join(', ')}');

      // Check each consecutive pair of real entry dates for gaps > 62 days
      for (int i = 1; i < realEntries.length; i++) {
        final previous = realEntries[i - 1];
        final current = realEntries[i];
        final gapDays = current.difference(previous).inDays;

        print(
            'CHECKING GAP: $gapDays days from ${previous.toString().substring(0, 10)} to ${current.toString().substring(0, 10)}');

        if (gapDays > 62) {
          print(
              '*** FOUND LARGE GAP: $gapDays days from ${previous.toString().substring(0, 10)} to ${current.toString().substring(0, 10)} ***');

          // Check if this specific gap has a record gap entry that covers it
          final hasRecordGapEntry =
              _hasRecordGapEntryForGap(previous, current, meterReadings);

          print('*** GAP COVERED BY RECORD ENTRY: $hasRecordGapEntry ***');

          if (!hasRecordGapEntry) {
            print('*** CREATING VALIDATION ISSUE FOR THIS GAP ***');
            // Create validation issue for this gap
            final message = ValidationMessageBuilder.buildMissingEntryMessage(
              gapDays: gapDays,
              startDate: previous,
              endDate: current,
            );

            issues.add(ValidationIssue(
              type: ValidationIssueType.missingEntry,
              severity: ValidationIssueSeverity.low,
              message: message,
              metadata: {
                'start_date': previous.toIso8601String(),
                'end_date': current.toIso8601String(),
                'gap_days': gapDays,
              },
            ));

            Logger.info(
                'SimpleGapDetectionService: Found gap of $gapDays days from ${previous.toString().substring(0, 10)} to ${current.toString().substring(0, 10)}');
          } else {
            Logger.info(
                'SimpleGapDetectionService: Gap from ${previous.toString().substring(0, 10)} to ${current.toString().substring(0, 10)} has record gap entry, skipping');
          }
        }
      }

      Logger.info(
          'SimpleGapDetectionService: Found ${issues.length} missing entry gaps');
      print('=== GAP DETECTION COMPLETE: Found ${issues.length} gaps ===');
      return issues;
    } catch (e) {
      Logger.error('SimpleGapDetectionService: Error during gap detection: $e');
      return [];
    }
  }

  /// Dismiss a gap by creating a Records Gap entry in the history table
  Future<void> dismissGap(ValidationIssue issue) async {
    try {
      final startDate = DateTime.parse(issue.metadata!['start_date']);
      final endDate = DateTime.parse(issue.metadata!['end_date']);
      final gapDays = issue.metadata!['gap_days'] as int;

      Logger.info(
          'SimpleGapDetectionService: Creating Records Gap entry for $gapDays days from $startDate to $endDate');

      // Calculate the date for the Records Gap entry (middle of gap period)
      final gapEntryDate = startDate.add(Duration(days: (gapDays / 2).round()));

      // Create a Records Gap entry
      final recordsGapEntry = MeterReading(
        value: 0.0,
        date: gapEntryDate,
        status: EntryStatus.ignored,
        notes:
            'Records Gap: $gapDays days (${_formatDateForNotes(startDate)} - ${_formatDateForNotes(endDate)})',
      );

      // Add the Records Gap entry to the database
      await _meterReadingRepository.addMeterReading(recordsGapEntry);

      Logger.info(
          'SimpleGapDetectionService: Successfully created Records Gap entry');
    } catch (e) {
      Logger.error(
          'SimpleGapDetectionService: Error creating Records Gap entry: $e');
      rethrow;
    }
  }

  /// Format date for notes display
  String _formatDateForNotes(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  /// Check if a specific gap has a record gap entry that covers it
  bool _hasRecordGapEntryForGap(
      DateTime gapStart, DateTime gapEnd, List<MeterReading> meterReadings) {
    print(
        'CHECKING COVERAGE FOR GAP: ${gapStart.toString().substring(0, 10)} to ${gapEnd.toString().substring(0, 10)}');

    for (final reading in meterReadings) {
      // Check if this is a record gap entry
      if (_isRecordGapEntry(reading)) {
        print(
            'FOUND RECORD GAP ENTRY: ${reading.date.toString().substring(0, 10)} - ${reading.notes}');

        // Parse the gap dates from the record gap entry's notes
        final gapDates = _parseGapDatesFromNotes(reading.notes);
        if (gapDates != null) {
          final recordGapStart = gapDates['start'];
          final recordGapEnd = gapDates['end'];

          print(
              'PARSED RECORD GAP DATES: ${recordGapStart?.toString().substring(0, 10)} to ${recordGapEnd?.toString().substring(0, 10)}');

          // Check if this record gap entry covers the gap we're checking
          // The record gap entry covers this gap if the detected gap matches
          // the record gap period exactly (the gap that was originally dismissed)
          if (recordGapStart != null && recordGapEnd != null) {
            // Check if the detected gap matches the record gap exactly
            final gapMatchesRecord = (_isSameDay(gapStart, recordGapStart) || gapStart.isAtSameMomentAs(recordGapStart)) &&
                                   (_isSameDay(gapEnd, recordGapEnd) || gapEnd.isAtSameMomentAs(recordGapEnd));

            print(
                'COVERAGE CHECK: gapStart matches recordStart: ${_isSameDay(gapStart, recordGapStart)}, gapEnd matches recordEnd: ${_isSameDay(gapEnd, recordGapEnd)}, exact match: $gapMatchesRecord');

            if (gapMatchesRecord) {
              print('RECORD GAP EXACTLY MATCHES THIS GAP!');
              Logger.info(
                  'SimpleGapDetectionService: Record gap entry exactly matches detected gap ${gapStart.toString().substring(0, 10)} to ${gapEnd.toString().substring(0, 10)}');
              return true;
            }
          }
        } else {
          print('FAILED TO PARSE RECORD GAP DATES FROM: ${reading.notes}');
        }
      }
    }
    print('NO RECORD GAP ENTRY COVERS THIS GAP');
    return false;
  }

  /// Parse gap dates from record gap entry notes
  /// Expected format: "Records Gap: X days (DD/MM/YYYY - DD/MM/YYYY)"
  Map<String, DateTime>? _parseGapDatesFromNotes(String? notes) {
    if (notes == null || !notes.contains('Records Gap:')) return null;

    // Extract the date range part: (DD/MM/YYYY - DD/MM/YYYY)
    final dateRangeMatch =
        RegExp(r'\((\d{2}/\d{2}/\d{4}) - (\d{2}/\d{2}/\d{4})\)')
            .firstMatch(notes);
    if (dateRangeMatch == null) return null;

    try {
      final startDateStr = dateRangeMatch.group(1)!;
      final endDateStr = dateRangeMatch.group(2)!;

      // Parse DD/MM/YYYY format
      final startParts = startDateStr.split('/');
      final endParts = endDateStr.split('/');

      final startDate = DateTime(
        int.parse(startParts[2]), // year
        int.parse(startParts[1]), // month
        int.parse(startParts[0]), // day
      );

      final endDate = DateTime(
        int.parse(endParts[2]), // year
        int.parse(endParts[1]), // month
        int.parse(endParts[0]), // day
      );

      return {'start': startDate, 'end': endDate};
    } catch (e) {
      Logger.warning(
          'SimpleGapDetectionService: Failed to parse gap dates from notes: $notes');
      return null;
    }
  }

  /// Simple detection of record gap entries
  bool _isRecordGapEntry(MeterReading reading) {
    return reading.status == EntryStatus.ignored &&
        reading.notes?.contains('Records Gap:') == true;
  }

  /// Check if two dates are on the same day
  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }
}
