// File: lib/features/cost/presentation/models/chart_data.dart

/// Represents a data point for cost charts
class ChartData {
  /// Date of the data point
  final DateTime date;

  /// Usage value for this data point
  final double usage;

  /// Cost value for this data point
  final double cost;

  /// Whether this data point represents the start of a gap
  final bool isGapStart;

  /// Whether this data point represents the end of a gap
  final bool isGapEnd;

  /// Constructor
  const ChartData({
    required this.date,
    required this.usage,
    required this.cost,
    this.isGapStart = false,
    this.isGapEnd = false,
  });

  /// Create a copy with updated values
  ChartData copyWith({
    DateTime? date,
    double? usage,
    double? cost,
    bool? isGapStart,
    bool? isGapEnd,
  }) {
    return ChartData(
      date: date ?? this.date,
      usage: usage ?? this.usage,
      cost: cost ?? this.cost,
      isGapStart: isGapStart ?? this.isGapStart,
      isGapEnd: isGapEnd ?? this.isGapEnd,
    );
  }

  @override
  String toString() {
    return 'ChartData(date: $date, usage: $usage, cost: $cost, isGapStart: $isGapStart, isGapEnd: $isGapEnd)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChartData &&
        other.date == date &&
        other.usage == usage &&
        other.cost == cost;
  }

  @override
  int get hashCode {
    return date.hashCode ^ usage.hashCode ^ cost.hashCode;
  }
}
