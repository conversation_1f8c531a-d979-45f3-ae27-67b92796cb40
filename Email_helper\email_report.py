import requests
import webbrowser
import json
import urllib.parse
from http.server import HTTPServer, BaseHTTPRequestHandler
from openai import OpenAI

# === CONFIGURATION ===
CLIENT_ID = "f9c9f68d-1eab-4d0c-9c32-a2fb12a6c38c"
TENANT_ID = "1574194d-635f-41d8-ba1a-57fad83817da"
CLIENT_SECRET = "****************************************"
REDIRECT_URI = "http://localhost:8000"
AUTHORITY = f"https://login.microsoftonline.com/{TENANT_ID}"
SCOPE = "https://graph.microsoft.com/.default"
GRAPH_API_ENDPOINT = "https://graph.microsoft.com/v1.0"

# === QWEN (LM Studio) Client ===
qwen_client = OpenAI(
    base_url="http://************:1234/v1",
    api_key="not-needed"
)

# === Step 1: Get Auth Code via Browser ===
class OAuthHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        query = urllib.parse.urlparse(self.path).query
        params = urllib.parse.parse_qs(query)
        self.server.auth_code = params.get('code', [None])[0]
        self.send_response(200)
        self.end_headers()
        self.wfile.write(b"Authentication complete. You may close this window.")

def get_auth_code():
    auth_url = (
        f"{AUTHORITY}/oauth2/v2.0/authorize"
        f"?client_id={CLIENT_ID}"
        f"&response_type=code"
        f"&redirect_uri={urllib.parse.quote(REDIRECT_URI)}"
        f"&response_mode=query"
        f"&scope=Mail.Read User.Read offline_access"
    )
    print("Opening browser for login...")
    webbrowser.open(auth_url)

    server = HTTPServer(("localhost", 8000), OAuthHandler)
    server.handle_request()
    return server.auth_code

# === Step 2: Exchange Code for Access Token ===
def get_access_token(auth_code):
    token_url = f"{AUTHORITY}/oauth2/v2.0/token"
    data = {
        "client_id": CLIENT_ID,
        "scope": "https://graph.microsoft.com/Mail.Read https://graph.microsoft.com/User.Read",
        "code": auth_code,
        "redirect_uri": REDIRECT_URI,
        "grant_type": "authorization_code",
        "client_secret": CLIENT_SECRET
    }
    headers = {"Content-Type": "application/x-www-form-urlencoded"}
    response = requests.post(token_url, data=data, headers=headers)
    if response.status_code != 200:
        print("Failed to get access token:\n", response.text)
        return None
    return response.json()["access_token"]

# === Step 3: Fetch Emails ===
def get_emails(access_token, top=10):
    headers = {"Authorization": f"Bearer {access_token}"}
    url = f"{GRAPH_API_ENDPOINT}/me/messages?$top={top}&$select=subject,bodyPreview,receivedDateTime,from"
    response = requests.get(url, headers=headers)
    if response.status_code != 200:
        print("Failed to fetch emails:", response.text)
        return []
    return response.json().get("value", [])

# === Step 4: Ask Qwen to Summarize ===
def analyze_email(email):
    prompt = f"""You are an assistant that analyzes emails for house repair issues.

Email subject: {email['subject']}
Email preview: {email['bodyPreview']}

Determine if this email is related to a house repair. If yes, summarize it and list any repair types, companies, quotes, and dates. If not, reply "Not relevant".
Return result in readable plain text format.
"""
    response = qwen_client.chat.completions.create(
        model="qwen:14b",
        messages=[
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": prompt}
        ]
    )
    return response.choices[0].message.content.strip()

# === MAIN EXECUTION ===
if __name__ == "__main__":
    print("🔐 Getting authorization code...")
    auth_code = get_auth_code()

    print("🔑 Getting access token...")
    token = get_access_token(auth_code)
    if not token:
        exit(1)

    print("📥 Fetching emails...")
    emails = get_emails(token, top=10)

    print("\n📋 Generating House Repair Report:\n")
    for email in emails:
        print(f"📬 Subject: {email['subject']}")
        print(f"📅 Date: {email['receivedDateTime']}")
        print(f"👤 From: {email['from']['emailAddress']['address']}")
        print(f"📝 Preview: {email['bodyPreview'][:100]}")
        print("🤖 Qwen Analysis:")
        analysis = analyze_email(email)
        print(analysis)
        print("-" * 60)
