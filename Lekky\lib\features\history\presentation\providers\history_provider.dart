import 'dart:async';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../../core/providers/error_provider.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/utils/event_bus.dart';
import '../../../../core/shared/enums/entry_enums.dart';
import '../../domain/models/history_state.dart';
import '../services/history_filter_service.dart';
import '../services/history_pagination_service.dart';
import '../services/history_data_loader.dart';

part 'history_provider.g.dart';

/// History provider with smart pagination and auto-refresh on data updates
@riverpod
class History extends _$History {
  StreamSubscription<EventType>? _eventSubscription;

  @override
  Future<HistoryState> build() async {
    // Set up event listener for data updates
    _setupEventListener();

    // Dispose event subscription when provider is disposed
    ref.onDispose(() {
      _eventSubscription?.cancel();
    });

    return await _loadInitialState();
  }

  /// Load initial state
  Future<HistoryState> _loadInitialState() async {
    try {
      final initialState = HistoryState.initial();
      await _loadEntries(initialState);
      return state.value ?? initialState;
    } catch (error, stackTrace) {
      ref.read(globalErrorProvider.notifier).handleError(
            error,
            stackTrace,
            context: 'History initialization',
          );
      return HistoryState.initial().copyWith(
        errorMessage: 'Failed to load history: ${error.toString()}',
      );
    }
  }

  /// Check if any filters are active (delegated to service)
  bool hasActiveFilters(HistoryState historyState) {
    return HistoryFilterService.hasActiveFilters(historyState);
  }

  /// Set filter type and reload entries
  Future<void> setFilterType(EntryFilterType filterType) async {
    final currentState = state.value ?? HistoryState.initial();
    if (currentState.filterType == filterType) return;

    final newState = HistoryFilterService.applyFilters(
      currentState,
      filterType: filterType,
    );
    state = AsyncValue.data(newState);
    await _loadEntries(newState);
  }

  /// Set sort order and reload entries
  Future<void> setSortOrder(EntrySortOrder sortOrder) async {
    final currentState = state.value ?? HistoryState.initial();
    if (currentState.sortOrder == sortOrder) return;

    final newState = HistoryFilterService.applyFilters(
      currentState,
      sortOrder: sortOrder,
      resetPagination: false,
    );
    state = AsyncValue.data(newState);
    await _loadEntries(newState);
  }

  /// Set date range filter and reload entries
  Future<void> setDateRange(DateTime? startDate, DateTime? endDate) async {
    final currentState = state.value ?? HistoryState.initial();
    if (currentState.startDate == startDate &&
        currentState.endDate == endDate) {
      return;
    }

    final newState = HistoryFilterService.applyFilters(
      currentState,
      startDate: startDate,
      endDate: endDate,
    );
    state = AsyncValue.data(newState);
    await _loadEntries(newState);
  }

  /// Set all filters atomically and reload entries
  Future<void> setAllFilters({
    required EntryFilterType filterType,
    required EntrySortOrder sortOrder,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final currentState = state.value ?? HistoryState.initial();

    // Check if any filters have actually changed
    if (currentState.filterType == filterType &&
        currentState.sortOrder == sortOrder &&
        currentState.startDate == startDate &&
        currentState.endDate == endDate) {
      Logger.info(
          'HistoryProvider: No filter changes detected, skipping update');
      return;
    }

    // Validate date range if provided
    if (!HistoryFilterService.isValidDateRange(startDate, endDate)) {
      Logger.warning(
          'HistoryProvider: Invalid date range - start date is after end date');
      return;
    }

    Logger.info('HistoryProvider: Applying filters atomically');

    state = AsyncValue.data(currentState.copyWith(
      filterType: filterType,
      sortOrder: sortOrder,
      startDate: startDate,
      endDate: endDate,
      currentPage: 0, // Reset to first page
      isLoading: true,
    ));

    await _loadEntries(state.value!);
  }

  /// Clear all filters and reset to defaults
  Future<void> clearAllFilters() async {
    await setAllFilters(
      filterType: EntryFilterType.all,
      sortOrder: EntrySortOrder.newestFirst,
      startDate: null,
      endDate: null,
    );
  }

  /// Go to next page
  Future<void> nextPage() async {
    final currentState = state.value ?? HistoryState.initial();
    final newState = HistoryPaginationService.goToNextPage(currentState);
    if (newState == currentState) return;

    state = AsyncValue.data(newState);
    await _loadEntries(newState);
  }

  /// Go to previous page
  Future<void> previousPage() async {
    final currentState = state.value ?? HistoryState.initial();
    final newState = HistoryPaginationService.goToPreviousPage(currentState);
    if (newState == currentState) return;

    state = AsyncValue.data(newState);
    await _loadEntries(newState);
  }

  /// Go to specific page
  Future<void> goToPage(int page) async {
    final currentState = state.value ?? HistoryState.initial();
    final newState = HistoryPaginationService.goToPage(currentState, page);
    if (newState == currentState) return;

    state = AsyncValue.data(newState);
    await _loadEntries(newState);
  }

  /// Go to first page
  Future<void> goToFirstPage() async {
    final currentState = state.value ?? HistoryState.initial();
    final newState = HistoryPaginationService.goToFirstPage(currentState);
    if (newState == currentState) return;

    state = AsyncValue.data(newState);
    await _loadEntries(newState);
  }

  /// Go to last page
  Future<void> goToLastPage() async {
    final currentState = state.value ?? HistoryState.initial();
    final newState = HistoryPaginationService.goToLastPage(currentState);
    if (newState == currentState) return;

    state = AsyncValue.data(newState);
    await _loadEntries(newState);
  }

  /// Refresh data
  Future<void> refresh() async {
    final currentState = state.value ?? HistoryState.initial();
    state = AsyncValue.data(currentState.copyWith(isLoading: true));
    await _loadEntries(state.value!);
  }

  /// Set up event listener for data updates
  void _setupEventListener() {
    _eventSubscription?.cancel(); // Cancel any existing subscription
    _eventSubscription = EventBus().stream.listen((event) {
      Logger.info('HistoryProvider: Received event: $event');
      if (event == EventType.dataUpdated ||
          event == EventType.dateSettingsUpdated) {
        Logger.info(
            'HistoryProvider: Received data update or date settings event, refreshing history');
        // Refresh the history when data is updated or date settings change (maintains current page/scroll position)
        refresh();
      } else if (event == EventType.averagesCalculating) {
        Logger.info(
            'HistoryProvider: Averages calculating, showing loading state');
        final currentState = state.value ?? HistoryState.initial();
        state = AsyncValue.data(currentState.copyWith(isLoading: true));
      } else if (event == EventType.averageCalculationFailed) {
        Logger.info(
            'HistoryProvider: Average calculation failed, clearing loading state');
        final currentState = state.value ?? HistoryState.initial();
        state = AsyncValue.data(currentState.copyWith(isLoading: false));
      }
    });
  }

  /// Clear error
  void clearError() {
    final currentState = state.value ?? HistoryState.initial();
    state = AsyncValue.data(currentState.copyWith(errorMessage: null));
  }

  /// Smart pagination logic with accumulative filtering
  Future<void> _loadEntries(HistoryState currentState) async {
    try {
      Logger.info(
          'HistoryProvider: Loading entries with filters - ${HistoryFilterService.getActiveFiltersSummary(currentState)}');

      final result =
          await HistoryDataLoader.loadEntriesWithCount(ref, currentState);

      if (!HistoryDataLoader.validateLoadedData(result)) {
        throw Exception('Invalid data structure returned from loader');
      }

      final paginatedEntries = result['entries'] as List<dynamic>;
      final totalCount = result['totalCount'] as int;

      Logger.info(
          'HistoryProvider: Loaded ${paginatedEntries.length} entries ($totalCount total) for page ${currentState.currentPage + 1}');

      // Update state using pagination service
      final newState = HistoryPaginationService.updatePaginationState(
        currentState,
        totalCount: totalCount,
        entries: paginatedEntries,
      );

      state = AsyncValue.data(newState);
    } catch (error, stackTrace) {
      Logger.error('Failed to load history entries: $error', stackTrace);

      state = AsyncValue.data(currentState.copyWith(
        isLoading: false,
        errorMessage: 'Failed to load entries: ${error.toString()}',
      ));

      ref.read(globalErrorProvider.notifier).handleError(
            error,
            stackTrace,
            context: 'History entries loading',
          );
    }
  }
}
