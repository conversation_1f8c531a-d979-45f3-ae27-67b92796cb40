import 'package:sqflite/sqflite.dart';
import '../../../../core/utils/logger.dart';
import '../domain/models/notification.dart';
import '../domain/repositories/notification_repository.dart';

/// Implementation of the notification repository
class NotificationRepositoryImpl implements NotificationRepository {
  /// Database instance
  final Database _database;

  /// Table name
  static const String _tableName = 'notifications';

  /// Constructor
  NotificationRepositoryImpl(this._database);

  /// Create the notifications table
  static Future<void> createTable(Database db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS $_tableName (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        message TEXT NOT NULL,
        timestamp TEXT NOT NULL,
        type INTEGER NOT NULL,
        isRead INTEGER NOT NULL DEFAULT 0
      )
    ''');
  }

  @override
  Future<List<AppNotification>> getAllNotifications() async {
    try {
      final List<Map<String, dynamic>> maps = await _database.query(
        _tableName,
        orderBy: 'timestamp DESC',
      );

      return List.generate(maps.length, (i) {
        return AppNotification.fromMap(maps[i]);
      });
    } catch (e) {
      Logger.error('Failed to get all notifications: $e');
      return [];
    }
  }

  @override
  Future<List<AppNotification>> getUnreadNotifications() async {
    try {
      final List<Map<String, dynamic>> maps = await _database.query(
        _tableName,
        where: 'isRead = ?',
        whereArgs: [0],
        orderBy: 'timestamp DESC',
      );

      return List.generate(maps.length, (i) {
        return AppNotification.fromMap(maps[i]);
      });
    } catch (e) {
      Logger.error('Failed to get unread notifications: $e');
      return [];
    }
  }

  @override
  Future<AppNotification?> getNotificationById(int id) async {
    try {
      final List<Map<String, dynamic>> maps = await _database.query(
        _tableName,
        where: 'id = ?',
        whereArgs: [id],
      );

      if (maps.isNotEmpty) {
        return AppNotification.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      Logger.error('Failed to get notification by id: $e');
      return null;
    }
  }

  @override
  Future<int> addNotification(AppNotification notification) async {
    try {
      return await _database.insert(
        _tableName,
        notification.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      Logger.error('Failed to add notification: $e');
      return -1;
    }
  }

  @override
  Future<void> markAsRead(int id) async {
    try {
      await _database.update(
        _tableName,
        {'isRead': 1},
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      Logger.error('Failed to mark notification as read: $e');
    }
  }

  @override
  Future<void> markAllAsRead() async {
    try {
      await _database.update(
        _tableName,
        {'isRead': 1},
      );
    } catch (e) {
      Logger.error('Failed to mark all notifications as read: $e');
    }
  }

  @override
  Future<void> deleteNotification(int id) async {
    try {
      await _database.delete(
        _tableName,
        where: 'id = ?',
        whereArgs: [id],
      );
    } catch (e) {
      Logger.error('Failed to delete notification: $e');
    }
  }

  @override
  Future<void> deleteAllNotifications() async {
    try {
      await _database.delete(_tableName);
    } catch (e) {
      Logger.error('Failed to delete all notifications: $e');
    }
  }

  @override
  Future<int> getUnreadCount() async {
    try {
      final result = await _database.rawQuery(
        'SELECT COUNT(*) as count FROM $_tableName WHERE isRead = 0',
      );
      return Sqflite.firstIntValue(result) ?? 0;
    } catch (e) {
      Logger.error('Failed to get unread count: $e');
      return 0;
    }
  }

  @override
  Future<List<AppNotification>> getNotificationsByType(
      NotificationType type) async {
    try {
      final List<Map<String, dynamic>> maps = await _database.query(
        _tableName,
        where: 'type = ?',
        whereArgs: [type.index],
        orderBy: 'timestamp DESC',
      );

      return List.generate(maps.length, (i) {
        return AppNotification.fromMap(maps[i]);
      });
    } catch (e) {
      Logger.error('Failed to get notifications by type: $e');
      return [];
    }
  }

  @override
  Future<void> deleteNotificationsByType(NotificationType type) async {
    try {
      await _database.delete(
        _tableName,
        where: 'type = ?',
        whereArgs: [type.index],
      );
    } catch (e) {
      Logger.error('Failed to delete notifications by type: $e');
    }
  }
}
