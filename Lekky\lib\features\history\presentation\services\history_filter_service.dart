// File: lib/features/history/presentation/services/history_filter_service.dart

import '../../../../core/shared/enums/entry_enums.dart';
import '../../domain/models/history_state.dart';

/// Service for managing history filters and validation
class HistoryFilterService {
  /// Check if any filters are active (non-default)
  static bool hasActiveFilters(HistoryState state) {
    return state.filterType != EntryFilterType.all ||
        state.sortOrder != EntrySortOrder.newestFirst ||
        state.startDate != null ||
        state.endDate != null;
  }

  /// Check if date range filter is valid
  static bool isValidDateRange(DateTime? startDate, DateTime? endDate) {
    if (startDate == null || endDate == null) return true;
    return !startDate.isAfter(endDate);
  }

  /// Get active filter summary for debugging/logging
  static String getActiveFiltersSummary(HistoryState state) {
    final filters = <String>[];

    if (state.filterType != EntryFilterType.all) {
      filters.add('Type: ${state.filterType.name}');
    }

    if (state.sortOrder != EntrySortOrder.newestFirst) {
      filters.add('Sort: ${state.sortOrder.name}');
    }

    if (state.startDate != null && state.endDate != null) {
      filters.add(
          'Date: ${state.startDate!.toIso8601String().split('T')[0]} to ${state.endDate!.toIso8601String().split('T')[0]}');
    }

    return filters.isEmpty
        ? 'No active filters'
        : 'Active filters: ${filters.join(', ')}';
  }

  /// Check if filters have changed between two states
  static bool filtersChanged(HistoryState oldState, HistoryState newState) {
    return oldState.filterType != newState.filterType ||
        oldState.sortOrder != newState.sortOrder ||
        oldState.startDate != newState.startDate ||
        oldState.endDate != newState.endDate;
  }

  /// Create default filter state
  static HistoryState createDefaultFilterState() {
    return HistoryState.initial();
  }

  /// Apply filters to create new state with reset pagination
  static HistoryState applyFilters(
    HistoryState currentState, {
    EntryFilterType? filterType,
    EntrySortOrder? sortOrder,
    DateTime? startDate,
    DateTime? endDate,
    bool resetPagination = true,
  }) {
    return currentState.copyWith(
      filterType: filterType ?? currentState.filterType,
      sortOrder: sortOrder ?? currentState.sortOrder,
      startDate: startDate,
      endDate: endDate,
      currentPage: resetPagination ? 0 : currentState.currentPage,
      isLoading: true,
    );
  }

  /// Clear all filters and return to defaults
  static HistoryState clearAllFilters(HistoryState currentState) {
    return applyFilters(
      currentState,
      filterType: EntryFilterType.all,
      sortOrder: EntrySortOrder.newestFirst,
      startDate: null,
      endDate: null,
      resetPagination: true,
    );
  }
}
