import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/widgets/app_banner.dart';
import '../../../../core/providers/settings_navigation_provider.dart';

/// Tips & Tricks screen
class TipsTricksScreen extends StatelessWidget {
  /// Constructor
  const TipsTricksScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) async {
        if (didPop) return;
        context.go('/main-settings?expanded=${SettingsCategoryIndex.about}');
      },
      child: Scaffold(
        body: Column(
          children: [
            // Banner with back arrow
            GestureDetector(
              onTap: () => context
                  .go('/main-settings?expanded=${SettingsCategoryIndex.about}'),
              child: AppBanner(
                message: '← Tips & Tricks',
                gradientColors: AppColors.getSettingsMainCardGradient(
                    Theme.of(context).brightness == Brightness.dark),
                textColor: AppColors.getAppBarTextColor('settings',
                    Theme.of(context).brightness == Brightness.dark),
              ),
            ),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Tips & Tricks section
                    Card(
                      margin: const EdgeInsets.only(bottom: 16.0),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Row(
                              children: [
                                Icon(Icons.lightbulb, color: Colors.amber),
                                SizedBox(width: 16),
                                Text(
                                  'Tips & Tricks',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // Tips list
                            _buildTipItem(
                              'Enter readings at consistent times for more accurate averages',
                              color: Colors.amber,
                            ),
                            _buildTipItem(
                              'Recent average charts show breaks in the line when Records Gap entries are present',
                              color: Colors.amber,
                            ),
                            _buildTipItem(
                              'Records Gap entries appear as "No Meter Reading" in history when validation gaps are dismissed',
                              color: Colors.amber,
                            ),
                            _buildTipItem(
                              'Use the history screen to identify usage patterns',
                              color: Colors.amber,
                            ),
                            _buildTipItem(
                              'Set up notifications to stay on top of your usage',
                              color: Colors.amber,
                            ),
                            _buildTipItem(
                              'Regular top-ups help maintain a steady credit balance',
                              color: Colors.amber,
                            ),
                            _buildTipItem(
                              'Check the cost screen to monitor your spending',
                              color: Colors.amber,
                            ),

                            // Notification-specific tips
                            _buildNotificationTipSection(),

                            // Permissions section
                            _buildPermissionsTipSection(),

                            // File locations section
                            _buildFileLocationsTipSection(),

                            // Troubleshooting section
                            _buildTroubleshootingTipSection(),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build a tip item
  Widget _buildTipItem(String tip, {Color color = Colors.green}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.check_circle,
            color: color,
            size: 16,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              tip,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  /// Build notification-specific tips section
  Widget _buildNotificationTipSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),

        // Notification tips header
        const Row(
          children: [
            Icon(Icons.notifications_active, size: 18, color: Colors.orange),
            SizedBox(width: 8),
            Text(
              'Notification Tips',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.orange,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // Notification tips
        _buildNotificationTip(
          Icons.notifications,
          'Tap the notification bell icon on the Dashboard to view all notifications',
        ),
        _buildNotificationTip(
          Icons.settings,
          'Configure notification types in Settings → Alerts & Notifications',
        ),
        _buildNotificationTip(
          Icons.schedule,
          'Set up reminders to check your meter regularly',
        ),
        _buildNotificationTip(
          Icons.warning,
          'Enable low balance alerts to avoid running out of credit',
        ),
        _buildNotificationTip(
          Icons.build,
          'Use Utilities in Alerts & Notifications to manage permissions and reset notification data',
        ),
        _buildNotificationTip(
          Icons.refresh,
          'Use "Reset All Alert States" in Utilities to allow alerts to fire again immediately if conditions are met',
        ),
        _buildNotificationTip(
          Icons.bug_report,
          'Access Testing → Notification Debug for advanced troubleshooting and system status',
        ),
      ],
    );
  }

  /// Build a notification tip item
  Widget _buildNotificationTip(IconData icon, String tip) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: Colors.orange,
            size: 16,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              tip,
              style: const TextStyle(fontSize: 13),
            ),
          ),
        ],
      ),
    );
  }

  /// Build a file location tip item
  Widget _buildFileLocationTip(IconData icon, String tip) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: Colors.blue,
            size: 16,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              tip,
              style: const TextStyle(fontSize: 13),
            ),
          ),
        ],
      ),
    );
  }

  /// Build permissions tips section
  Widget _buildPermissionsTipSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),

        // Permissions tips header
        const Row(
          children: [
            Icon(Icons.security, size: 18, color: Colors.green),
            SizedBox(width: 8),
            Text(
              'Notification Permissions',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.green,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // Permission requirements
        _buildPermissionTip(
          Icons.android,
          'Android 13+ (API 33+): POST_NOTIFICATIONS permission must be granted for alerts to work',
        ),
        _buildPermissionTip(
          Icons.phone_iphone,
          'iOS: Alert, badge, and sound permissions must be granted for notifications',
        ),
        _buildPermissionTip(
          Icons.info_outline,
          'Older Android versions: Notification permissions are automatically granted',
        ),
        _buildPermissionTip(
          Icons.build,
          'Use "Request Permissions" in Settings → Alerts & Notifications → Utilities to easily grant permissions',
        ),
        _buildPermissionTip(
          Icons.lightbulb_outline,
          'Individual alert types (Low Balance, Threshold) work even if main notifications toggle is off',
        ),
        _buildPermissionTip(
          Icons.settings,
          'If notifications stop working, check your device notification settings for Lekky',
        ),
      ],
    );
  }

  /// Build file locations tips section
  Widget _buildFileLocationsTipSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),

        // File locations tips header
        const Row(
          children: [
            Icon(Icons.folder, size: 18, color: Colors.blue),
            SizedBox(width: 8),
            Text(
              'File Locations',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.blue,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // File location tips
        _buildFileLocationTip(
          Icons.file_download,
          'Export files are saved to: /storage/emulated/0/Download/Lekky/',
        ),
        _buildFileLocationTip(
          Icons.file_upload,
          'Import files from the same location or use file picker to browse',
        ),
        _buildFileLocationTip(
          Icons.folder_open,
          'Access exported files through Downloads app or any file manager',
        ),
        _buildFileLocationTip(
          Icons.share,
          'Files can be shared directly from the Downloads/Lekky folder',
        ),
        _buildFileLocationTip(
          Icons.phone_android,
          'On Android 15+, files use modern storage APIs for better security',
        ),
      ],
    );
  }

  /// Build troubleshooting tips section
  Widget _buildTroubleshootingTipSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),

        // Troubleshooting tips header
        const Row(
          children: [
            Icon(Icons.help_outline, size: 18, color: Colors.purple),
            SizedBox(width: 8),
            Text(
              'Troubleshooting',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.purple,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // Troubleshooting tips
        _buildTroubleshootingTip(
          Icons.notifications_off,
          'Not receiving notifications? Check Settings → Alerts & Notifications → Utilities → Request Permissions',
        ),
        _buildTroubleshootingTip(
          Icons.refresh,
          'Notifications acting up? Use "Clear All Notification Data" in Utilities to reset the system',
        ),
        _buildTroubleshootingTip(
          Icons.bug_report,
          'For detailed diagnostics, go to Settings → Testing → Notification Debug → Generate Status Report',
        ),
        _buildTroubleshootingTip(
          Icons.toggle_on,
          'Individual alert types work independently - you can enable Low Balance alerts without the main toggle',
        ),
        _buildTroubleshootingTip(
          Icons.phone_android,
          'On Android 13+, ensure "Allow notifications" is enabled in your device settings for Lekky',
        ),
        _buildTroubleshootingTip(
          Icons.schedule,
          'Meter reminders won\'t fire if you\'ve already taken a reading after 3 AM the same day',
        ),
      ],
    );
  }

  /// Build a troubleshooting tip item
  Widget _buildTroubleshootingTip(IconData icon, String tip) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: Colors.purple,
            size: 16,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              tip,
              style: const TextStyle(fontSize: 13),
            ),
          ),
        ],
      ),
    );
  }

  /// Build a permission tip item
  Widget _buildPermissionTip(IconData icon, String tip) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: Colors.green,
            size: 16,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              tip,
              style: const TextStyle(fontSize: 13),
            ),
          ),
        ],
      ),
    );
  }
}
