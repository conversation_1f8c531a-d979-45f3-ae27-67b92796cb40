// File: lib/features/cost/presentation/services/cost_date_service.dart

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/utils/logger.dart';
import '../../domain/models/cost_state.dart';
import '../../domain/models/cost_period.dart';
import '../../domain/models/date_range.dart';
import '../../data/cost_repository.dart';

/// Service for date range management and validation
class CostDateService {
  /// Validate custom date range
  static String? validateDateRange(DateTime? fromDate, DateTime? toDate) {
    if (fromDate == null || toDate == null) {
      return 'Both start and end dates are required';
    }

    if (fromDate.isAfter(toDate)) {
      return 'Start date cannot be after end date';
    }

    final now = DateTime.now();
    if (fromDate.isAfter(now)) {
      return 'Start date cannot be in the future';
    }

    // Check if date range is too large (more than 2 years)
    final daysDifference = toDate.difference(fromDate).inDays;
    if (daysDifference > 730) {
      return 'Date range cannot exceed 2 years';
    }

    return null; // Valid date range
  }

  /// Calculate default from date (previous meter reading)
  static Future<DateTime?> calculateDefaultFromDate(
    Ref ref,
    CostRepository costRepo,
  ) async {
    try {
      return await costRepo.getPreviousMeterReadingDate();
    } catch (e) {
      Logger.error(
          'CostDateService: Failed to calculate default from date: $e');
      return null;
    }
  }

  /// Calculate default to date (last meter reading)
  static Future<DateTime?> calculateDefaultToDate(
    Ref ref,
    CostRepository costRepo,
  ) async {
    try {
      final lastReadingDate = await costRepo.getLastMeterReadingDate();

      // Adjust to end of day to include the full day
      if (lastReadingDate != null) {
        return DateTime(
          lastReadingDate.year,
          lastReadingDate.month,
          lastReadingDate.day,
          23,
          59,
          59,
        );
      }
      return null;
    } catch (e) {
      Logger.error('CostDateService: Failed to calculate default to date: $e');
      return null;
    }
  }

  /// Get date range for a specific period
  static DateRange getDateRangeForPeriod(CostPeriod period) {
    return DateRange.forPeriod(period);
  }

  /// Adjust to date to end of day
  static DateTime adjustToEndOfDay(DateTime date) {
    return DateTime(
      date.year,
      date.month,
      date.day,
      23,
      59,
      59,
    );
  }

  /// Check if sufficient data exists for custom period
  static Future<bool> hasSufficientDataForCustomPeriod(
    Ref ref,
    CostRepository costRepo,
  ) async {
    try {
      return await costRepo.hasSufficientMeterReadings();
    } catch (e) {
      Logger.error('CostDateService: Failed to check sufficient data: $e');
      return false;
    }
  }

  /// Get default date range for custom period
  static Future<Map<String, DateTime?>> getDefaultCustomDateRange(
    Ref ref,
    CostRepository costRepo,
  ) async {
    final fromDate = await calculateDefaultFromDate(ref, costRepo);
    final toDate = await calculateDefaultToDate(ref, costRepo);

    return {
      'fromDate': fromDate,
      'toDate': toDate,
    };
  }

  /// Create cost state with updated date range
  static CostState updateStateWithDateRange(
    CostState currentState, {
    DateTime? fromDate,
    DateTime? toDate,
  }) {
    // Adjust toDate to end of day if provided
    DateTime? adjustedToDate = toDate;
    if (toDate != null) {
      adjustedToDate = adjustToEndOfDay(toDate);
    }

    // Validate the date range
    final dateRangeError = validateDateRange(fromDate, adjustedToDate);

    return currentState.copyWith(
      fromDate: fromDate,
      toDate: adjustedToDate,
      dateRangeError: dateRangeError,
      isLoading: dateRangeError == null, // Only set loading if valid
    );
  }

  /// Handle custom period selection with default dates
  static Future<CostState> handleCustomPeriodSelection(
    Ref ref,
    CostRepository costRepo,
    CostState currentState,
  ) async {
    try {
      // Check if there are sufficient meter readings
      final hasSufficientData =
          await hasSufficientDataForCustomPeriod(ref, costRepo);

      if (!hasSufficientData) {
        Logger.warning(
            'CostDateService: Insufficient meter readings for custom period');
        return currentState.copyWith(
          selectedPeriod: CostPeriod.custom,
          fromDate: null,
          toDate: null,
          dateRangeError:
              'Insufficient meter readings for custom period analysis',
          isLoading: false,
        );
      }

      // Get default date range
      final defaultDates = await getDefaultCustomDateRange(ref, costRepo);
      final fromDate = defaultDates['fromDate'];
      final toDate = defaultDates['toDate'];

      if (fromDate == null || toDate == null) {
        Logger.warning(
            'CostDateService: Could not determine default date range');
        return currentState.copyWith(
          selectedPeriod: CostPeriod.custom,
          fromDate: null,
          toDate: null,
          dateRangeError: 'Could not determine default date range',
          isLoading: false,
        );
      }

      // Validate the default date range
      final dateRangeError = validateDateRange(fromDate, toDate);

      Logger.info(
          'CostDateService: Custom period selected with default dates - From: $fromDate, To: $toDate');

      return currentState.copyWith(
        selectedPeriod: CostPeriod.custom,
        fromDate: fromDate,
        toDate: toDate,
        dateRangeError: dateRangeError,
        isLoading: dateRangeError == null,
      );
    } catch (error) {
      Logger.error(
          'CostDateService: Error handling custom period selection: $error');
      return currentState.copyWith(
        selectedPeriod: CostPeriod.custom,
        fromDate: null,
        toDate: null,
        dateRangeError: 'Failed to set up custom period: ${error.toString()}',
        isLoading: false,
      );
    }
  }

  /// Get date range summary for logging
  static String getDateRangeSummary(DateTime? fromDate, DateTime? toDate) {
    if (fromDate == null && toDate == null) return 'No date range set';

    final from = fromDate?.toIso8601String().split('T')[0] ?? 'N/A';
    final to = toDate?.toIso8601String().split('T')[0] ?? 'N/A';

    return 'Date range: $from to $to';
  }

  /// Check if date range is valid for calculations
  static bool isValidForCalculation(DateTime? fromDate, DateTime? toDate) {
    return fromDate != null &&
        toDate != null &&
        validateDateRange(fromDate, toDate) == null;
  }

  /// Get period display name
  static String getPeriodDisplayName(CostPeriod period) {
    switch (period) {
      case CostPeriod.pastDay:
      case CostPeriod.futureDay:
        return 'Day';
      case CostPeriod.pastWeek:
      case CostPeriod.futureWeek:
        return 'Week';
      case CostPeriod.pastMonth:
      case CostPeriod.futureMonth:
        return 'Month';
      case CostPeriod.pastYear:
      case CostPeriod.futureYear:
        return 'Year';
      case CostPeriod.custom:
        return 'Custom Period';
      default:
        return period.toString();
    }
  }
}
