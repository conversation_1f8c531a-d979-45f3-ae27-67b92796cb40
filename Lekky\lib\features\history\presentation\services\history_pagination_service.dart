// File: lib/features/history/presentation/services/history_pagination_service.dart

import '../../domain/models/history_state.dart';

/// Service for managing history pagination logic
class HistoryPaginationService {
  /// Check if can go to next page
  static bool canGoNext(HistoryState state) {
    return state.currentPage < state.totalPages - 1;
  }

  /// Check if can go to previous page
  static bool canGoPrevious(HistoryState state) {
    return state.currentPage > 0;
  }

  /// Calculate total pages based on total count and page size
  static int calculateTotalPages(int totalCount, int pageSize) {
    if (totalCount == 0) return 1;
    return (totalCount / pageSize).ceil();
  }

  /// Get pagination info for display
  static Map<String, dynamic> getPaginationInfo(HistoryState state) {
    final startItem = state.currentPage * state.entriesPerPage + 1;
    final endItem = ((state.currentPage + 1) * state.entriesPerPage)
        .clamp(0, state.totalEntries);

    return {
      'currentPage': state.currentPage + 1, // 1-based for display
      'totalPages': state.totalPages,
      'startItem': startItem,
      'endItem': endItem,
      'totalCount': state.totalEntries,
      'pageSize': state.entriesPerPage,
    };
  }

  /// Navigate to next page
  static HistoryState goToNextPage(HistoryState currentState) {
    if (!canGoNext(currentState)) return currentState;

    return currentState.copyWith(
      currentPage: currentState.currentPage + 1,
      isLoading: true,
    );
  }

  /// Navigate to previous page
  static HistoryState goToPreviousPage(HistoryState currentState) {
    if (!canGoPrevious(currentState)) return currentState;

    return currentState.copyWith(
      currentPage: currentState.currentPage - 1,
      isLoading: true,
    );
  }

  /// Navigate to specific page
  static HistoryState goToPage(HistoryState currentState, int page) {
    if (page < 0 ||
        page >= currentState.totalPages ||
        page == currentState.currentPage) {
      return currentState;
    }

    return currentState.copyWith(
      currentPage: page,
      isLoading: true,
    );
  }

  /// Navigate to first page
  static HistoryState goToFirstPage(HistoryState currentState) {
    if (currentState.currentPage == 0) return currentState;

    return currentState.copyWith(
      currentPage: 0,
      isLoading: true,
    );
  }

  /// Navigate to last page
  static HistoryState goToLastPage(HistoryState currentState) {
    final lastPage = currentState.totalPages - 1;
    if (currentState.currentPage == lastPage) return currentState;

    return currentState.copyWith(
      currentPage: lastPage,
      isLoading: true,
    );
  }

  /// Update pagination state with new data
  static HistoryState updatePaginationState(
    HistoryState currentState, {
    required int totalCount,
    required List<dynamic> entries,
  }) {
    final totalPages =
        calculateTotalPages(totalCount, currentState.entriesPerPage);

    return currentState.copyWith(
      entries: entries,
      totalEntries: totalCount,
      totalPages: totalPages,
      isLoading: false,
      errorMessage: null,
    );
  }

  /// Reset pagination to first page
  static HistoryState resetPagination(HistoryState currentState) {
    return currentState.copyWith(
      currentPage: 0,
      isLoading: true,
    );
  }

  /// Validate page bounds and adjust if necessary
  static HistoryState validateAndAdjustPage(HistoryState currentState) {
    if (currentState.currentPage >= currentState.totalPages) {
      final adjustedPage =
          (currentState.totalPages - 1).clamp(0, double.infinity).toInt();
      return currentState.copyWith(currentPage: adjustedPage);
    }
    return currentState;
  }
}
