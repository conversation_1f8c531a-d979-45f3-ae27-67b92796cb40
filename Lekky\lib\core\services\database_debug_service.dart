import 'package:flutter/foundation.dart';
import '../utils/database_inspector.dart';
import '../utils/logger.dart';

/// Service for debugging database issues during development
class DatabaseDebugService {
  /// Investigate Entry ID 214 specifically (the dismissed entry from screenshots)
  static Future<void> investigateEntry214() async {
    if (kDebugMode) {
      Logger.info(
          'DatabaseDebugService: Starting investigation of Entry ID 214');
      Logger.debug('Starting investigation of Entry ID 214');
      await DatabaseInspector.investigateEntry(214, isMeterReading: true);
      Logger.debug('Completed investigation of Entry ID 214');
    }
  }

  /// Investigate all dismissed entries in the database
  static Future<void> investigateAllDismissedEntries() async {
    if (kDebugMode) {
      try {
        Logger.info(
            'DatabaseDebugService: Starting investigation of all dismissed entries');

        final dbInfo = await DatabaseInspector.getDetailedDatabaseInfo();
        final validationStatus = dbInfo['validation_status'];

        if (validationStatus != null) {
          final ignoredCount =
              validationStatus['meter_readings']['ignored'] as int;
          Logger.info(
              'DatabaseDebugService: Found $ignoredCount dismissed meter readings');

          if (ignoredCount > 0) {
            // Log general database state
            await DatabaseInspector.logDatabaseContents();
          }
        }
      } catch (e) {
        Logger.error(
            'DatabaseDebugService: Error investigating dismissed entries: $e');
      }
    }
  }

  /// Run comprehensive database investigation
  static Future<void> runFullInvestigation() async {
    if (kDebugMode) {
      Logger.info('DatabaseDebugService: Starting full database investigation');

      // Investigate Entry ID 214 specifically
      await investigateEntry214();

      // Find all entries with dismissal notes
      await findAllDismissalEntries();

      // Investigate all dismissed entries
      await investigateAllDismissedEntries();

      Logger.info('DatabaseDebugService: Full investigation completed');
    }
  }

  /// Find all entries with dismissal notes to identify the correct dismissed entry
  static Future<void> findAllDismissalEntries() async {
    if (kDebugMode) {
      try {
        Logger.info(
            'DatabaseDebugService: Finding all entries with dismissal notes');
        Logger.debug('Searching for entries with dismissal notes...');

        final dbInfo = await DatabaseInspector.findEntriesWithDismissalNotes();
        Logger.debug('Found ${dbInfo.length} entries with dismissal notes');

        for (final entry in dbInfo) {
          final id = entry['id'];
          final value = entry['value'];
          final status = entry['status'];
          final date = entry['date'];
          final notes = entry['notes'];
          Logger.debug(
              'DEBUG: Entry ID $id - Value: $value, Status: $status, Date: $date');
          Logger.debug('Notes: $notes');
          Logger.debug('---');

          // Investigate this specific entry in detail
          await DatabaseInspector.investigateEntry(id, isMeterReading: true);
        }
      } catch (e) {
        Logger.error(
            'DatabaseDebugService: Error finding dismissal entries: $e');
        Logger.debug('Error finding dismissal entries: $e');
      }
    }
  }
}
