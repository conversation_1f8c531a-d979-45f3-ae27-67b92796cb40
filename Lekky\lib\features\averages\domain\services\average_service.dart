import '../../../../core/models/average.dart';
import '../../../../core/models/meter_entry.dart';
import '../../../../core/models/per_reading_average.dart';
import '../../../../core/shared/enums/entry_enums.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/utils/event_bus.dart';
import '../../../../core/utils/performance_monitor.dart';
import '../../../../core/utils/average_calculator.dart';
import '../../../../core/utils/average_calculation_lock.dart';
import '../../../../core/di/service_locator.dart';
import '../../../meter_readings/domain/repositories/meter_reading_repository.dart';
import '../../../top_ups/domain/repositories/top_up_repository.dart';
import '../repositories/average_repository.dart';
import '../repositories/per_reading_average_repository.dart';

/// Service for managing average calculations and caching
class AverageService {
  final AverageRepository _averageRepository;
  final PerReadingAverageRepository _perReadingAverageRepository;
  final MeterReadingRepository _meterReadingRepository;
  final TopUpRepository _topUpRepository;

  /// Constructor
  AverageService({
    AverageRepository? averageRepository,
    PerReadingAverageRepository? perReadingAverageRepository,
    MeterReadingRepository? meterReadingRepository,
    TopUpRepository? topUpRepository,
  })  : _averageRepository =
            averageRepository ?? serviceLocator<AverageRepository>(),
        _perReadingAverageRepository = perReadingAverageRepository ??
            serviceLocator<PerReadingAverageRepository>(),
        _meterReadingRepository =
            meterReadingRepository ?? serviceLocator<MeterReadingRepository>(),
        _topUpRepository = topUpRepository ?? serviceLocator<TopUpRepository>();

  /// Get averages directly from database
  Future<AverageResult> getAverages() async {
    try {
      Logger.info('AverageService: Loading averages from database');

      // Get cached averages from database
      final cachedAverages = await _averageRepository.getLatestAverages();

      // Get recent average from latest per-reading average
      final recentAverage = await _getRecentAverage();

      Logger.info(
          'AverageService: Loaded averages - Recent: $recentAverage, Total: ${cachedAverages?.totalAverage}');

      return AverageResult(
        recentAverage: recentAverage,
        totalAverage: cachedAverages?.totalAverage,
      );
    } catch (e) {
      Logger.error('AverageService: Error getting averages: $e');
      return const AverageResult(
        recentAverage: null,
        totalAverage: null,
      );
    }
  }

  /// Update averages after data changes
  Future<void> updateAverages() async {
    final lock = AverageCalculationLock();

    await lock.executeWithLock(() async {
      await PerformanceMonitor.timeOperation('average_calculation', () async {
        try {
          Logger.info('AverageService: Updating averages after data change');

          // Fire event to show loading state
          Logger.info('AverageService: Firing averagesCalculating event');
          EventBus().fire(EventType.averagesCalculating);

          await _calculateAndCacheAverages();
          await _calculateAndStorePerReadingAverages();

          Logger.info('AverageService: Average update completed successfully');

          // Fire completion event after averages are fully calculated and cached
          Logger.info('AverageService: Firing dataUpdated event');
          EventBus().fire(EventType.dataUpdated);
          Logger.info('AverageService: dataUpdated event fired successfully');
        } catch (e) {
          Logger.error('AverageService: Error updating averages: $e');

          // Fire event to indicate calculation failed
          EventBus().fire(EventType.averageCalculationFailed);

          rethrow;
        }
      });
    });
  }

  /// Calculate and cache fresh averages
  Future<AverageResult> _calculateAndCacheAverages() async {
    try {
      // Get all data
      final meterReadings = await _meterReadingRepository.getAllMeterReadings();
      final topUps = await _topUpRepository.getAllTopUps();

      // Convert to MeterEntry format for calculation - exclude dismissal entries
      final List<MeterEntry> entries = [
        ...meterReadings
            .where((reading) =>
                reading.status != EntryStatus.ignored &&
                !(reading.notes?.contains('Records Gap:') == true))
            .map((reading) => MeterEntry(
                  id: reading.id,
                  date: reading.date,
                  reading: reading.value,
                  amountToppedUp: 0.0,
                  typeCode: 0,
                  notes: reading.notes,
                )),
        ...topUps.map((topUp) => MeterEntry(
              id: topUp.id,
              date: topUp.date,
              reading: 0.0,
              amountToppedUp: topUp.amount,
              typeCode: 1,
              notes: topUp.notes,
            )),
      ];

      // Calculate averages using existing logic
      Logger.info(
          'AverageService: Calculating total average from ${entries.length} entries');
      final totalAverage = AverageCalculator.calculateTotalAverage(entries);
      Logger.info('AverageService: Total average calculated: $totalAverage');

      // Calculate and store per-reading averages for historical accuracy first
      await _calculateAndStorePerReadingAverages();

      // Get recent average from latest per-reading average
      final recentAverage = await _getRecentAverage();

      // Cache the results
      final average = Average(
        recentAverage: recentAverage,
        totalAverage: totalAverage,
        lastUpdated: DateTime.now(),
      );

      await _averageRepository.saveAverages(average);

      Logger.info(
          'AverageService: Calculated and cached averages - Recent: $recentAverage, Total: $totalAverage');

      return AverageResult(
        recentAverage: recentAverage,
        totalAverage: totalAverage,
      );
    } catch (e) {
      Logger.error('AverageService: Error calculating averages: $e');
      rethrow;
    }
  }

  /// Get recent average from latest per-reading average
  Future<double?> _getRecentAverage() async {
    try {
      Logger.info(
          'AverageService: Getting recent average from latest per-reading average');

      // Get the latest per-reading average
      final latestPerReadingAverage =
          await _perReadingAverageRepository.getLatestPerReadingAverage();

      if (latestPerReadingAverage != null) {
        Logger.info(
            'AverageService: Found latest per-reading average: ${latestPerReadingAverage.recentAveragePerDay} for reading date: ${latestPerReadingAverage.readingDate}');
        return latestPerReadingAverage.recentAveragePerDay;
      }

      Logger.info(
          'AverageService: No per-reading averages found, returning null');
      return null;
    } catch (e) {
      Logger.error(
          'AverageService: Error getting recent average from per-reading averages: $e');
      return null;
    }
  }

  /// Calculate and store per-reading averages
  Future<void> _calculateAndStorePerReadingAverages() async {
    try {
      Logger.info('AverageService: Calculating per-reading averages');

      // Get all meter readings sorted by date - exclude dismissal entries
      final allMeterReadings =
          await _meterReadingRepository.getAllMeterReadings();
      final meterReadings = allMeterReadings
          .where((reading) => reading.status != EntryStatus.ignored)
          .toList();
      final topUps = await _topUpRepository.getAllTopUps();

      Logger.info(
          'AverageService: Found ${meterReadings.length} meter readings and ${topUps.length} top-ups');

      if (meterReadings.length < 2) {
        Logger.info(
            'AverageService: Not enough meter readings for per-reading averages (${meterReadings.length})');
        return;
      }

      // Sort readings by date
      meterReadings.sort((a, b) => a.date.compareTo(b.date));

      Logger.info(
          'AverageService: Processing ${meterReadings.length - 1} per-reading average calculations');

      // Calculate recent average for each reading after the first
      for (int i = 1; i < meterReadings.length; i++) {
        final currentReading = meterReadings[i];
        final previousReading = meterReadings[i - 1];

        final days =
            currentReading.date.difference(previousReading.date).inDays;

        Logger.info(
            'AverageService: Processing reading ${i + 1}/$meterReadings.length - ${currentReading.date} ($days days from previous)');

        if (days > 0) {
          // Calculate top-ups between these two readings
          double topUpsBetween = 0;
          for (var topUp in topUps) {
            if (topUp.date.isAfter(previousReading.date) &&
                topUp.date.isBefore(currentReading.date)) {
              topUpsBetween += topUp.amount;
            }
          }

          // Calculate usage: previous reading - current reading + top-ups
          final usage =
              previousReading.value - currentReading.value + topUpsBetween;

          Logger.info(
              'AverageService: Reading calculation - Previous: ${previousReading.value}, Current: ${currentReading.value}, Top-ups: $topUpsBetween, Usage: $usage, Days: $days');

          double recentAveragePerDay;
          if (usage > 0) {
            // If gap is >62 days, use total average as fallback
            if (days > 62) {
              // Get total average from all entries
              final allEntries = [
                ...meterReadings.map((reading) => MeterEntry(
                      id: reading.id,
                      date: reading.date,
                      reading: reading.value,
                      amountToppedUp: 0.0,
                      typeCode: 0,
                      notes: reading.notes,
                    )),
                ...topUps.map((topUp) => MeterEntry(
                      id: topUp.id,
                      date: topUp.date,
                      reading: 0.0,
                      amountToppedUp: topUp.amount,
                      typeCode: 1,
                      notes: topUp.notes,
                    )),
              ];
              recentAveragePerDay =
                  AverageCalculator.calculateTotalAverage(allEntries);
              Logger.info(
                  'AverageService: Using total average $recentAveragePerDay for large gap of $days days');
            } else {
              recentAveragePerDay = usage / days;
            }

            Logger.info(
                'AverageService: Calculated per-reading average: $recentAveragePerDay per day for reading ${currentReading.id}');

            // Create and save per-reading average
            final perReadingAverage = PerReadingAverage(
              meterReadingId: currentReading.id!,
              readingDate: currentReading.date,
              recentAveragePerDay: recentAveragePerDay,
              calculatedAt: DateTime.now(),
            );

            await _perReadingAverageRepository
                .savePerReadingAverage(perReadingAverage);
          } else {
            Logger.info(
                'AverageService: Skipping reading ${currentReading.id} - invalid usage: $usage');
          }
        } else {
          Logger.info(
              'AverageService: Skipping reading ${currentReading.id} - invalid days: $days');
        }
      }

      Logger.info('AverageService: Per-reading averages calculated and stored');
    } catch (e) {
      Logger.error(
          'AverageService: Error calculating per-reading averages: $e');
    }
  }
}

/// Result class for average calculations
class AverageResult {
  final double? recentAverage;
  final double? totalAverage;

  const AverageResult({
    this.recentAverage,
    this.totalAverage,
  });
}
