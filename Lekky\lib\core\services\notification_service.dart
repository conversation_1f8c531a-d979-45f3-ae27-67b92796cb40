import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/preference_keys.dart';

/// Service for managing notification settings
class NotificationService extends ChangeNotifier {
  /// Whether notifications are enabled
  bool _notificationsEnabled = false;

  /// Whether low balance alerts are enabled
  bool _lowBalanceAlertsEnabled = false;

  /// Whether time to top up alerts are enabled
  bool _timeToTopUpAlertsEnabled = false;

  /// Whether invalid record alerts are enabled
  bool _invalidRecordAlertsEnabled = false;

  /// Get whether notifications are enabled
  bool get notificationsEnabled => _notificationsEnabled;

  /// Get whether low balance alerts are enabled
  bool get lowBalanceAlertsEnabled => _lowBalanceAlertsEnabled;

  /// Get whether time to top up alerts are enabled
  bool get timeToTopUpAlertsEnabled => _timeToTopUpAlertsEnabled;

  /// Get whether invalid record alerts are enabled
  bool get invalidRecordAlertsEnabled => _invalidRecordAlertsEnabled;

  /// Constructor
  NotificationService() {
    _loadNotificationSettings();
  }

  /// Load notification settings from preferences
  Future<void> _loadNotificationSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Check if any notification type is enabled
      final lowBalanceEnabled =
          prefs.getBool(PreferenceKeys.lowBalanceAlertsEnabled) ?? false;
      final timeToTopUpEnabled =
          prefs.getBool(PreferenceKeys.timeToTopUpAlertsEnabled) ?? false;
      final invalidRecordEnabled =
          prefs.getBool(PreferenceKeys.invalidRecordAlertsEnabled) ?? false;
      _notificationsEnabled =
          lowBalanceEnabled || timeToTopUpEnabled || invalidRecordEnabled;
      _lowBalanceAlertsEnabled =
          prefs.getBool(PreferenceKeys.lowBalanceAlertsEnabled) ?? false;
      _timeToTopUpAlertsEnabled =
          prefs.getBool(PreferenceKeys.timeToTopUpAlertsEnabled) ?? false;
      _invalidRecordAlertsEnabled =
          prefs.getBool(PreferenceKeys.invalidRecordAlertsEnabled) ?? false;

      notifyListeners();
    } catch (e) {
      // Handle error
      debugPrint('Error loading notification settings: $e');
    }
  }

  /// Set whether notifications are enabled
  Future<void> setNotificationsEnabled(bool enabled) async {
    if (_notificationsEnabled == enabled) return;

    try {
      // Note: Master toggle removed - individual toggles control notifications
      _notificationsEnabled = enabled;

      // If notifications are disabled, disable all specific notification types
      if (!enabled) {
        await setLowBalanceAlertsEnabled(false);
        await setTimeToTopUpAlertsEnabled(false);
        await setInvalidRecordAlertsEnabled(false);
      }

      notifyListeners();
    } catch (e) {
      // Handle error
      debugPrint('Error setting notifications enabled: $e');
    }
  }

  /// Set whether low balance alerts are enabled
  Future<void> setLowBalanceAlertsEnabled(bool enabled) async {
    if (_lowBalanceAlertsEnabled == enabled) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(PreferenceKeys.lowBalanceAlertsEnabled, enabled);

      _lowBalanceAlertsEnabled = enabled;
      notifyListeners();
    } catch (e) {
      // Handle error
      debugPrint('Error setting low balance alerts enabled: $e');
    }
  }

  /// Set whether time to top up alerts are enabled
  Future<void> setTimeToTopUpAlertsEnabled(bool enabled) async {
    if (_timeToTopUpAlertsEnabled == enabled) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(PreferenceKeys.timeToTopUpAlertsEnabled, enabled);

      _timeToTopUpAlertsEnabled = enabled;
      notifyListeners();
    } catch (e) {
      // Handle error
      debugPrint('Error setting time to top up alerts enabled: $e');
    }
  }

  /// Set whether invalid record alerts are enabled
  Future<void> setInvalidRecordAlertsEnabled(bool enabled) async {
    if (_invalidRecordAlertsEnabled == enabled) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(PreferenceKeys.invalidRecordAlertsEnabled, enabled);

      _invalidRecordAlertsEnabled = enabled;
      notifyListeners();
    } catch (e) {
      // Handle error
      debugPrint('Error setting invalid record alerts enabled: $e');
    }
  }
}
