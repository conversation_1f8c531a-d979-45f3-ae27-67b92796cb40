// File: lib/features/data_management/data/csv_parser.dart
import 'dart:io';
import 'package:csv/csv.dart';
import 'package:intl/intl.dart';
import '../../../core/models/meter_entry.dart';
import '../../../core/utils/logger.dart';
import '../../../core/utils/result.dart';
import '../../../core/utils/app_error.dart';
import '../../../core/utils/error_types.dart';

/// A parser for CSV files that can extract meter readings and top-ups
class CsvParser {
  /// Logger instance
  final logger = Logger('CsvParser');

  /// Supported date formats for parsing
  final List<DateFormat> _supportedDateFormats = [
    // ISO 8601 format is handled separately with DateTime.parse
    DateFormat('dd/MM/yyyy HH:mm'),
    DateFormat('dd/MM/yyyy'),
    DateFormat('dd-MM-yyyy HH:mm'),
    DateFormat('dd-MM-yyyy'),
    DateFormat('MM/dd/yyyy HH:mm'),
    DateFormat('MM/dd/yyyy'),
    DateFormat('yyyy/MM/dd HH:mm'),
    DateFormat('yyyy/MM/dd'),
    DateFormat('yyyy-MM-dd HH:mm'),
    DateFormat('yyyy-MM-dd'),
  ];

  /// Parse a CSV file and extract meter entries
  ///
  /// Returns a Result with a list of MeterEntry objects on success or an AppError on failure
  Future<Result<List<MeterEntry>>> parseFile(File file) async {
    try {
      // Read file content
      final csvString = await file.readAsString();

      // Check if the file is empty
      if (csvString.trim().isEmpty) {
        return Result.failure(AppError(
          message: 'The CSV file is empty',
          type: ErrorType.invalidData,
          severity: ErrorSeverity.medium,
        ));
      }

      // Convert CSV string to table
      final csvTable = const CsvToListConverter().convert(csvString);

      // Validate file format
      if (csvTable.isEmpty) {
        return Result.failure(AppError(
          message: 'No data found in CSV file',
          type: ErrorType.invalidData,
          severity: ErrorSeverity.medium,
        ));
      }

      // Detect headers and column mapping
      final headerMap = _detectHeaders(csvTable.first);
      if (headerMap.isEmpty) {
        return Result.failure(AppError(
          message: 'Could not detect valid headers in CSV file',
          type: ErrorType.invalidData,
          severity: ErrorSeverity.medium,
        ));
      }

      // Parse entries
      final entries = await _parseEntries(csvTable.sublist(1), headerMap);
      if (entries.isEmpty) {
        return Result.failure(AppError(
          message: 'No valid entries found in CSV file',
          type: ErrorType.invalidData,
          severity: ErrorSeverity.medium,
        ));
      }

      // Sort entries by timestamp to ensure chronological order
      entries.sort((a, b) => a.timestamp.compareTo(b.timestamp));

      logger.i('Successfully parsed ${entries.length} entries from CSV file');
      return Result.success(entries);
    } catch (e) {
      logger.e('Failed to parse CSV file', details: e.toString());
      return Result.failure(AppError(
        message: 'Failed to parse CSV file: $e',
        type: ErrorType.fileIOError,
        severity: ErrorSeverity.high,
      ));
    }
  }

  /// Detect headers in the CSV file and map them to column indices
  ///
  /// Returns a map of header names to column indices
  Map<String, int> _detectHeaders(List<dynamic> headerRow) {
    final headerMap = <String, int>{};
    final normalizedHeaders = <String, String>{};

    // Define possible header names for each field
    final dateHeaders = ['date', 'timestamp', 'time', 'datetime', 'date time'];
    final typeHeaders = [
      'type',
      'entry type',
      'entrytype',
      'category',
      'reading type'
    ];
    final valueHeaders = [
      'value',
      'amount',
      'reading',
      'meter reading',
      'top-up',
      'topup'
    ];
    final notesHeaders = [
      'notes',
      'note',
      'comment',
      'comments',
      'description',
      'desc'
    ];

    // Normalize header row
    for (int i = 0; i < headerRow.length; i++) {
      final header = headerRow[i].toString().trim().toLowerCase();
      normalizedHeaders[header] = header;

      // Check for variations with spaces or hyphens removed
      final noSpaces = header.replaceAll(' ', '');
      normalizedHeaders[noSpaces] = header;

      final noHyphens = header.replaceAll('-', '');
      normalizedHeaders[noHyphens] = header;

      final noSpacesOrHyphens = header.replaceAll(' ', '').replaceAll('-', '');
      normalizedHeaders[noSpacesOrHyphens] = header;
    }

    // Find date column
    for (final dateHeader in dateHeaders) {
      for (final entry in normalizedHeaders.entries) {
        if (entry.key.contains(dateHeader)) {
          final index = headerRow.indexWhere(
              (h) => h.toString().trim().toLowerCase() == entry.value);
          if (index >= 0) {
            headerMap['date'] = index;
            break;
          }
        }
      }
      if (headerMap.containsKey('date')) break;
    }

    // Find type column
    for (final typeHeader in typeHeaders) {
      for (final entry in normalizedHeaders.entries) {
        if (entry.key.contains(typeHeader)) {
          final index = headerRow.indexWhere(
              (h) => h.toString().trim().toLowerCase() == entry.value);
          if (index >= 0) {
            headerMap['type'] = index;
            break;
          }
        }
      }
      if (headerMap.containsKey('type')) break;
    }

    // Find value column
    for (final valueHeader in valueHeaders) {
      for (final entry in normalizedHeaders.entries) {
        if (entry.key.contains(valueHeader)) {
          final index = headerRow.indexWhere(
              (h) => h.toString().trim().toLowerCase() == entry.value);
          if (index >= 0) {
            headerMap['value'] = index;
            break;
          }
        }
      }
      if (headerMap.containsKey('value')) break;
    }

    // Find notes column (optional)
    for (final notesHeader in notesHeaders) {
      for (final entry in normalizedHeaders.entries) {
        if (entry.key.contains(notesHeader)) {
          final index = headerRow.indexWhere(
              (h) => h.toString().trim().toLowerCase() == entry.value);
          if (index >= 0) {
            headerMap['notes'] = index;
            break;
          }
        }
      }
      if (headerMap.containsKey('notes')) break;
    }

    // If we couldn't find the headers but have at least 3 columns, make a best guess
    if (headerMap.isEmpty && headerRow.length >= 3) {
      // Assume first column is date, second is type, third is value, fourth is notes (if available)
      headerMap['date'] = 0;
      headerMap['type'] = 1;
      headerMap['value'] = 2;
      if (headerRow.length >= 4) {
        headerMap['notes'] = 3;
      }
      logger.w(
          'Could not detect headers, using default mapping: date=0, type=1, value=2${headerRow.length >= 4 ? ', notes=3' : ''}');
    }

    return headerMap;
  }

  /// Parse entries from CSV data using the detected header mapping
  ///
  /// Returns a list of MeterEntry objects
  Future<List<MeterEntry>> _parseEntries(
      List<List<dynamic>> rows, Map<String, int> headerMap) async {
    final entries = <MeterEntry>[];
    int errorCount = 0;

    for (final row in rows) {
      try {
        // Skip rows that are too short
        if (row.length <= headerMap.values.reduce((a, b) => a > b ? a : b)) {
          continue;
        }

        // Extract values from the row
        final dateStr = row[headerMap['date']!].toString().trim();
        final typeStr = row[headerMap['type']!].toString().trim().toLowerCase();
        final valueStr = row[headerMap['value']!].toString().trim();
        final notesStr =
            headerMap.containsKey('notes') && row.length > headerMap['notes']!
                ? row[headerMap['notes']!].toString().trim()
                : null;

        // Parse date
        final date = _parseDate(dateStr);
        if (date == null) {
          logger.w('Could not parse date: $dateStr');
          errorCount++;
          continue;
        }

        // Parse value - handle special markers for dismissal entries
        double value;
        try {
          if (valueStr == '--') {
            value = 0.0; // Special marker for dismissal entries
          } else {
            value = double.parse(valueStr.replaceAll(',', '.'));
          }
        } catch (e) {
          logger.w('Could not parse value: $valueStr');
          errorCount++;
          continue;
        }

        // Determine entry type
        int typeCode;
        if (typeStr.contains('meter') ||
            typeStr.contains('reading') ||
            typeStr == '0') {
          typeCode = 0; // Meter Reading
        } else if (typeStr.contains('top') ||
            typeStr.contains('up') ||
            typeStr == '1') {
          typeCode = 1; // Top-up
        } else if (typeStr.contains('no meter') ||
            typeStr.contains('dismissed') ||
            typeStr == '2') {
          typeCode = 2; // Dismissal Entry
        } else {
          // Try to infer type from other columns or context
          typeCode = _inferTypeCode(typeStr, row, headerMap);
        }

        // Create entry
        final entry = MeterEntry.fromTypeCodeAndAmount(
          id: null,
          typeCode: typeCode,
          amount: value,
          timestamp: date,
          notes: notesStr?.isNotEmpty == true ? notesStr : null,
          // No averages - they will be recalculated
          shortAverageAfterTopUp: null,
          totalAverageUpToThisPoint: null,
        );

        entries.add(entry);
      } catch (e) {
        logger.e('Error parsing row',
            details: {'row': row, 'error': e.toString()});
        errorCount++;
      }
    }

    if (errorCount > 0) {
      logger.w(
          'Encountered $errorCount errors while parsing ${rows.length} rows');
    }

    return entries;
  }

  /// Parse a date string using multiple formats
  ///
  /// Returns a DateTime object or null if parsing fails
  DateTime? _parseDate(String dateStr) {
    // Try ISO 8601 format first
    try {
      return DateTime.parse(dateStr);
    } catch (_) {
      // Try other formats
      for (final format in _supportedDateFormats) {
        try {
          return format.parse(dateStr);
        } catch (_) {
          // Continue to next format
        }
      }
    }
    return null;
  }

  /// Infer the type code from the type string and other context
  ///
  /// Returns 0 for Meter Reading, 1 for Top-up, or 2 for Dismissal Entry
  int _inferTypeCode(
      String typeStr, List<dynamic> row, Map<String, int> headerMap) {
    // Check for dismissal keywords
    if (typeStr.contains('no meter') || typeStr.contains('dismissed')) {
      return 2; // Dismissal Entry
    }

    // Check if the type string contains any keywords
    if (typeStr.contains('meter') || typeStr.contains('reading')) {
      return 0; // Meter Reading
    }
    if (typeStr.contains('top') || typeStr.contains('up')) {
      return 1; // Top-up
    }

    // If we have a numeric type, try to parse it
    if (typeStr == '0' || typeStr == '1' || typeStr == '2') {
      return int.parse(typeStr);
    }

    // Default to meter reading
    return 0;
  }
}
