// File: lib/features/validation/presentation/controllers/validation_dashboard_controller.dart
import 'package:flutter/material.dart';
import '../../../../core/utils/logger.dart';
import '../../../meter_readings/domain/repositories/meter_reading_repository.dart';
import '../../../top_ups/domain/repositories/top_up_repository.dart';
import '../../domain/models/integrity_report.dart';
import '../../domain/models/validation_issue.dart';
import '../../domain/services/data_integrity_service.dart';

/// Filter type for validation issues
enum ValidationIssueFilterType {
  /// Show all issues
  all,

  /// Show only high severity issues
  highSeverity,

  /// Show only medium severity issues
  mediumSeverity,

  /// Show only low severity issues
  lowSeverity,
}

/// Controller for the Validation Dashboard screen
class ValidationDashboardController extends ChangeNotifier {
  final DataIntegrityService _dataIntegrityService;
  final MeterReadingRepository _meterReadingRepository;
  final TopUpRepository _topUpRepository;
  final logger = Logger('ValidationDashboardController');

  /// List of validation issues
  List<ValidationIssue> _issues = [];

  /// Filtered issues based on current filter
  List<ValidationIssue> _filteredIssues = [];

  /// Latest integrity report
  IntegrityReport? _integrityReport;

  /// Current filter type
  ValidationIssueFilterType _filterType = ValidationIssueFilterType.all;

  /// Loading state
  bool _isLoading = false;

  /// Error message
  String? _errorMessage;

  /// Constructor
  ValidationDashboardController({
    required DataIntegrityService dataIntegrityService,
    required MeterReadingRepository meterReadingRepository,
    required TopUpRepository topUpRepository,
  })  : _dataIntegrityService = dataIntegrityService,
        _meterReadingRepository = meterReadingRepository,
        _topUpRepository = topUpRepository;

  /// Get the list of all issues
  List<ValidationIssue> get issues => _issues;

  /// Get the list of filtered issues
  List<ValidationIssue> get filteredIssues => _filteredIssues;

  /// Get the latest integrity report
  IntegrityReport? get integrityReport => _integrityReport;

  /// Get the current filter type
  ValidationIssueFilterType get filterType => _filterType;

  /// Get the loading state
  bool get isLoading => _isLoading;

  /// Get the error message
  String? get errorMessage => _errorMessage;

  /// Initialize the controller
  Future<void> initialize() async {
    await loadIssues();
  }

  /// Load validation issues
  Future<void> loadIssues() async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      // Run integrity check
      _integrityReport = await _dataIntegrityService.checkIntegrity();
      _issues = _integrityReport?.issues ?? [];

      // Apply filter
      _applyFilter();

      _isLoading = false;
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Failed to load validation issues: $e';
      logger.e('Failed to load validation issues', details: e.toString());
    } finally {
      notifyListeners();
    }
  }

  /// Set the filter type
  void setFilterType(ValidationIssueFilterType filterType) {
    if (_filterType == filterType) return;

    _filterType = filterType;
    _applyFilter();
    notifyListeners();
  }

  /// Apply the current filter
  void _applyFilter() {
    switch (_filterType) {
      case ValidationIssueFilterType.all:
        _filteredIssues = List.from(_issues);
        break;

      case ValidationIssueFilterType.highSeverity:
        _filteredIssues = _issues
            .where((issue) => issue.severity == ValidationIssueSeverity.high)
            .toList();
        break;

      case ValidationIssueFilterType.mediumSeverity:
        _filteredIssues = _issues
            .where((issue) => issue.severity == ValidationIssueSeverity.medium)
            .toList();
        break;

      case ValidationIssueFilterType.lowSeverity:
        _filteredIssues = _issues
            .where((issue) => issue.severity == ValidationIssueSeverity.low)
            .toList();
        break;
    }
  }

  /// Get entry details for an issue
  Future<dynamic> getEntryForIssue(ValidationIssue issue) async {
    logger.i(
        'Attempting to get entry for issue: entryId=${issue.entryId}, type=${issue.type}');

    if (issue.entryId == null) {
      logger.w('Issue has null entryId, cannot retrieve entry');
      return null;
    }

    try {
      // Try to get meter reading
      logger.d('Searching for meter reading with ID: ${issue.entryId}');
      final meterReading =
          await _meterReadingRepository.getMeterReadingById(issue.entryId!);
      if (meterReading != null) {
        logger.i('Found meter reading for issue: ${meterReading.id}');
        return meterReading;
      }

      // Try to get top-up
      logger.d('Searching for top-up with ID: ${issue.entryId}');
      final topUp = await _topUpRepository.getTopUpById(issue.entryId!);
      if (topUp != null) {
        logger.i('Found top-up for issue: ${topUp.id}');
        return topUp;
      }

      logger.w(
          'No entry found for issue entryId: ${issue.entryId}, type: ${issue.type}');
      return null;
    } catch (e) {
      logger.e('Failed to get entry for issue entryId: ${issue.entryId}',
          details: e.toString());
      return null;
    }
  }

  /// Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  /// Refresh the data
  Future<void> refresh() async {
    await loadIssues();
  }

  /// Clear all filters and reset to show all issues
  void clearFilters() {
    setFilterType(ValidationIssueFilterType.all);
  }
}
