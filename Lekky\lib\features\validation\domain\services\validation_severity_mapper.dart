import '../models/validation_issue.dart';

/// Centralized mapping between ValidationIssueType and ValidationIssueSeverity
/// 
/// Provides consistent severity assignment across the validation system
class ValidationSeverityMapper {
  // Private constructor to prevent instantiation
  ValidationSeverityMapper._();

  /// Map of issue types to their corresponding severity levels
  static const Map<ValidationIssueType, ValidationIssueSeverity> _severityMap = {
    // High severity issues - critical problems that affect data integrity
    ValidationIssueType.negativeValue: ValidationIssueSeverity.high,
    ValidationIssueType.chronologicalOrder: ValidationIssueSeverity.high,
    ValidationIssueType.balanceInconsistency: ValidationIssueSeverity.high,
    ValidationIssueType.other: ValidationIssueSeverity.high,

    // Medium severity issues - should be fixed but not critical
    ValidationIssueType.futureDate: ValidationIssueSeverity.medium,
    ValidationIssueType.duplicateEntry: ValidationIssueSeverity.medium,

    // Low severity issues - informational, minor concerns
    ValidationIssueType.missingEntry: ValidationIssueSeverity.low,
  };

  /// Get severity for a specific validation issue type
  static ValidationIssueSeverity getSeverityForIssueType(ValidationIssueType type) {
    return _severityMap[type] ?? ValidationIssueSeverity.high;
  }

  /// Get the highest severity from a list of validation issues
  static ValidationIssueSeverity? getHighestSeverity(List<ValidationIssue> issues) {
    if (issues.isEmpty) return null;

    ValidationIssueSeverity? highest;
    for (final issue in issues) {
      final severity = getSeverityForIssueType(issue.type);
      if (highest == null || _compareSeverity(severity, highest) > 0) {
        highest = severity;
      }
    }
    return highest;
  }

  /// Check if an entry should be marked as invalid based on severity
  /// All severities mark entries as invalid (most inclusive approach)
  static bool shouldMarkAsInvalid(ValidationIssueSeverity severity) {
    return true; // All severities → EntryStatus.invalid
  }

  /// Check if any issues in the list should mark entries as invalid
  static bool hasInvalidatingIssues(List<ValidationIssue> issues) {
    return issues.any((issue) => shouldMarkAsInvalid(getSeverityForIssueType(issue.type)));
  }

  /// Get count of issues by severity level
  static Map<ValidationIssueSeverity, int> getSeverityCounts(List<ValidationIssue> issues) {
    final counts = <ValidationIssueSeverity, int>{
      ValidationIssueSeverity.high: 0,
      ValidationIssueSeverity.medium: 0,
      ValidationIssueSeverity.low: 0,
    };

    for (final issue in issues) {
      final severity = getSeverityForIssueType(issue.type);
      counts[severity] = (counts[severity] ?? 0) + 1;
    }

    return counts;
  }

  /// Compare two severities (returns positive if first is higher)
  static int _compareSeverity(ValidationIssueSeverity a, ValidationIssueSeverity b) {
    const severityOrder = {
      ValidationIssueSeverity.low: 1,
      ValidationIssueSeverity.medium: 2,
      ValidationIssueSeverity.high: 3,
    };
    return (severityOrder[a] ?? 0) - (severityOrder[b] ?? 0);
  }

  /// Get all issue types for a specific severity level
  static List<ValidationIssueType> getIssueTypesForSeverity(ValidationIssueSeverity severity) {
    return _severityMap.entries
        .where((entry) => entry.value == severity)
        .map((entry) => entry.key)
        .toList();
  }

  /// Check if a specific issue type should mark entries as invalid
  static bool shouldIssueTypeMarkAsInvalid(ValidationIssueType type) {
    final severity = getSeverityForIssueType(type);
    return shouldMarkAsInvalid(severity);
  }
}
