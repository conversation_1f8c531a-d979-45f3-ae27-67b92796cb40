import '../../features/meter_readings/domain/models/meter_reading.dart';
import '../shared/enums/entry_enums.dart';

/// Unified utility for detecting dismissed entries throughout the codebase
/// Single source of truth for dismissed entry detection logic
class DismissedEntryDetector {
  // Private constructor to prevent instantiation
  DismissedEntryDetector._();

  /// Check if a meter reading is a dismissed entry
  /// Uses simplified criteria: ONLY status == EntryStatus.ignored
  static bool isDismissedEntry(MeterReading reading) {
    return reading.status == EntryStatus.ignored;
  }

  /// Check if an entry matches dismissed criteria (for database inspection)
  /// Uses simplified criteria: ONLY status == 2 (ignored)
  static bool isDismissedEntryFromStatus(int? status) {
    return status == 2; // EntryStatus.ignored.value
  }
}
