from openai import OpenAI

# Connect to LM Studio (Qwen3-14B)
client = OpenAI(
    base_url="http://192.168.0.16:1234/v1",
    api_key="not-needed"  # Any non-empty string works
)

# Function to query the local model
def query_qwen(prompt: str) -> str:
    response = client.chat.completions.create(
        model="qwen:14b",  # Match exactly what LM Studio shows
        messages=[
            {"role": "system", "content": "You are an assistant that reads and analyzes email content."},
            {"role": "user", "content": prompt}
        ]
    )
    return response.choices[0].message.content

# Sample email to test
email = """Subject: Server Downtime Alert

Hi team, the main production server is currently down. Please investigate and resolve the issue as soon as possible."""

# Prompt to summarize and classify the email
prompt = f"""Summarize and categorize the following email.
Return output in this format:
Summary: <summary>
Category: <Work, Personal, Spam, Other>

Email:
{email}
"""

# Run it
if __name__ == "__main__":
    result = query_qwen(prompt)
    print("=== Qwen Response ===")
    print(result)
