// File: lib/features/cost/domain/models/cost_result.dart
import 'cost_period.dart';

/// Enum for calculation methods used
enum CalculationMethod {
  hybridHistorical, // Uses hybrid approach with short averages between readings
  totalAverageFallback, // Falls back to total average when hybrid fails
  totalAverageProjection, // Uses total average for future projections
  recentAverageProjection, // Uses recent average for projections
}

/// Result of a cost calculation
class CostResult {
  final double averageUsage;
  final double costPerPeriod;
  final CostPeriod period;
  final String meterUnit;
  final double? topUpAmount; // Amount topped up during the period
  final DateTime? topUpDate; // Date of the top-up
  final double? initialCredit; // Initial credit when moving in
  final DateTime? fromDate; // Start date of the period
  final DateTime? toDate; // End date of the period
  final int actualDays; // Actual number of days used in calculation
  final double dailyRate; // Cost per day
  final CalculationMethod calculationMethod; // Method used for calculation

  const CostResult({
    required this.averageUsage,
    required this.costPerPeriod,
    required this.period,
    required this.meterUnit,
    required this.actualDays,
    required this.dailyRate,
    required this.calculationMethod,
    this.topUpAmount,
    this.topUpDate,
    this.initialCredit,
    this.fromDate,
    this.toDate,
  });

  /// Creates a copy of this CostResult with the given fields replaced with the new values
  CostResult copyWith({
    double? averageUsage,
    double? costPerPeriod,
    CostPeriod? period,
    String? meterUnit,
    int? actualDays,
    double? dailyRate,
    CalculationMethod? calculationMethod,
    double? topUpAmount,
    DateTime? topUpDate,
    double? initialCredit,
    DateTime? fromDate,
    DateTime? toDate,
  }) {
    return CostResult(
      averageUsage: averageUsage ?? this.averageUsage,
      costPerPeriod: costPerPeriod ?? this.costPerPeriod,
      period: period ?? this.period,
      meterUnit: meterUnit ?? this.meterUnit,
      actualDays: actualDays ?? this.actualDays,
      dailyRate: dailyRate ?? this.dailyRate,
      calculationMethod: calculationMethod ?? this.calculationMethod,
      topUpAmount: topUpAmount ?? this.topUpAmount,
      topUpDate: topUpDate ?? this.topUpDate,
      initialCredit: initialCredit ?? this.initialCredit,
      fromDate: fromDate ?? this.fromDate,
      toDate: toDate ?? this.toDate,
    );
  }

  /// Format the cost per period as a string
  String get formattedCostPerPeriod =>
      '$meterUnit ${costPerPeriod.toStringAsFixed(2)}';

  /// Format the average usage as a string
  String get formattedAverageUsage =>
      '$meterUnit${averageUsage.toStringAsFixed(2)}/day';

  /// Format the top-up amount as a string
  String? get formattedTopUpAmount => topUpAmount != null
      ? '$meterUnit ${topUpAmount!.toStringAsFixed(2)}'
      : null;

  /// Check if there was a top-up during the period
  bool get hasTopUp => topUpAmount != null && topUpAmount! > 0;

  /// Check if there was initial credit when moving in
  bool get hasInitialCredit => initialCredit != null && initialCredit! > 0;

  /// Get the net cost (can be negative if there's initial credit)
  double get netCost {
    double result = costPerPeriod;
    if (hasInitialCredit) {
      result -= initialCredit!;
    }
    return result;
  }

  /// Format the net cost as a string (including credit indicator if negative)
  String get formattedNetCost {
    final cost = netCost;
    if (cost < 0) {
      return '$meterUnit ${(-cost).toStringAsFixed(2)} (credit)';
    } else {
      return '$meterUnit ${cost.toStringAsFixed(2)}';
    }
  }

  /// Get user-friendly description of calculation method
  String getCalculationMethodDescription() {
    switch (calculationMethod) {
      case CalculationMethod.hybridHistorical:
        return "Hybrid Historical Data";
      case CalculationMethod.totalAverageFallback:
        return "Total Average (Fallback)";
      case CalculationMethod.totalAverageProjection:
        return "Total Average";
      case CalculationMethod.recentAverageProjection:
        return "Recent Average";
    }
  }

  /// Get detailed description of calculation method
  String getDetailedCalculationDescription() {
    switch (calculationMethod) {
      case CalculationMethod.hybridHistorical:
        return "Uses short averages between meter readings for maximum accuracy";
      case CalculationMethod.totalAverageFallback:
        return "Uses overall average when detailed calculation unavailable";
      case CalculationMethod.totalAverageProjection:
        return "Projects future cost using overall usage pattern";
      case CalculationMethod.recentAverageProjection:
        return "Projects future cost using recent usage pattern";
    }
  }

  /// Get formatted description showing days, daily rate and calculation method
  String getFormattedDescription(String? legacyAverageType) {
    final dayText = actualDays == 1 ? 'day' : 'days';
    final methodDescription = getCalculationMethodDescription();

    // For hybrid historical calculations, show only days without daily rate
    if (calculationMethod == CalculationMethod.hybridHistorical) {
      return '$actualDays $dayText (Based on $methodDescription)';
    }

    // For other methods, show days with daily rate
    final formattedDailyRate = '$meterUnit${dailyRate.toStringAsFixed(2)}';
    return '$actualDays $dayText @ $formattedDailyRate/day (Based on $methodDescription)';
  }

  /// Format the initial credit as a string
  String? get formattedInitialCredit => initialCredit != null
      ? '$meterUnit ${initialCredit!.toStringAsFixed(2)}'
      : null;

  /// Check if this result has date range information
  bool get hasDateRange => fromDate != null && toDate != null;
}
