// File: lib/features/cost/presentation/services/cost_chart_service.dart

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/models/meter_entry.dart';
import '../../../../core/providers/database_provider.dart';
import '../../domain/models/cost_state.dart';
import '../../domain/models/cost_period.dart';
import '../models/chart_data.dart';
import '../models/chart_data_provider.dart';
import '../providers/cost_provider.dart';

/// Service for generating chart data for cost visualization
class CostChartService {
  /// Generate chart data for the current state
  static Future<List<ChartData>> generateChartData(
    Ref ref,
    CostState currentState,
  ) async {
    try {
      // Get actual meter entries from repositories
      final meterReadingRepo = ref.read(meterReadingRepositoryProvider);
      final topUpRepo = ref.read(topUpRepositoryProvider);

      // Get all historical data
      final meterReadings = await meterReadingRepo.getAllMeterReadings();
      final topUps = await topUpRepo.getAllTopUps();

      // Convert to MeterEntry objects for chart processing
      final meterEntries = <MeterEntry>[];

      // Add meter readings
      for (final reading in meterReadings) {
        meterEntries.add(MeterEntry(
          id: reading.id,
          date: reading.date,
          reading: reading.value,
          amountToppedUp: 0.0,
          typeCode: 0, // Meter reading
          notes: reading.notes,
        ));
      }

      // Add top-ups
      for (final topUp in topUps) {
        meterEntries.add(MeterEntry(
          id: topUp.id,
          date: topUp.date,
          reading: 0.0,
          amountToppedUp: topUp.amount,
          typeCode: 1, // Top-up
          notes: topUp.notes,
        ));
      }

      if (meterEntries.isEmpty) {
        Logger.info(
            'CostChartService: No meter entries found for chart generation');
        return [];
      }

      // Sort entries by date
      meterEntries.sort((a, b) => a.date.compareTo(b.date));

      // Generate chart data using ChartDataProvider
      final chartData = ChartDataProvider.generateChartData(
        entries: meterEntries,
        period: currentState.selectedPeriod,
        fromDate: currentState.fromDate,
        toDate: currentState.toDate,
      );

      Logger.info(
          'CostChartService: Generated ${chartData.length} chart data points from ${meterEntries.length} meter entries');

      return chartData;
    } catch (error) {
      Logger.error('CostChartService: Failed to generate chart data: $error');
      return [];
    }
  }

  /// Validate chart data
  static bool validateChartData(List<ChartData> chartData) {
    if (chartData.isEmpty) return false;

    // Check for valid data points
    return chartData.every((data) => data.usage >= 0 && data.cost >= 0);
  }

  /// Load recent average chart data from repository
  static Future<List<ChartData>> loadRecentAverageChartData(
    Ref ref,
    CostState currentState,
  ) async {
    try {
      final costRepo = ref.read(costRepositoryProvider);

      // Check if custom period with valid date range
      if (currentState.selectedPeriod == CostPeriod.custom &&
          currentState.fromDate != null &&
          currentState.toDate != null) {
        // Use date-filtered chart data for custom periods
        final chartData = await costRepo.getRecentAverageChartData(
          currentState.fromDate,
          currentState.toDate,
        );

        Logger.info(
            'CostChartService: Loaded ${chartData.length} recent average chart data points for custom period');
        return chartData;
      } else {
        // Use all averages (last 100) for standard periods
        final chartData =
            await costRepo.getRecentAverageChartDataForAllAverages();

        Logger.info(
            'CostChartService: Loaded ${chartData.length} recent average chart data points for standard period');
        return chartData;
      }
    } catch (error) {
      Logger.error(
          'CostChartService: Failed to load recent average chart data: $error');
      return [];
    }
  }

  /// Get chart data summary for logging
  static String getChartDataSummary(List<ChartData> chartData) {
    if (chartData.isEmpty) return 'No chart data available';

    final totalPoints = chartData.length;
    final dateRange = chartData.isNotEmpty
        ? '${chartData.first.date.toIso8601String().split('T')[0]} to ${chartData.last.date.toIso8601String().split('T')[0]}'
        : 'N/A';

    return 'Chart data: $totalPoints points, Range: $dateRange';
  }
}
